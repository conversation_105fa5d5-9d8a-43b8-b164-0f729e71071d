#!/usr/bin/env node

/**
 * Test script to verify entity discovery is working properly
 * This tests the complete flow from vector search to CandidateEntity conversion
 */

// Use the compiled Prisma client from dist
const { PrismaClient } = require('./dist/prisma/prisma.service.js').default || require('@prisma/client');

async function testEntityDiscovery() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Testing Entity Discovery Pipeline...\n');
    
    // Step 1: Test vector search (simulated)
    console.log('Step 1: Testing vector search...');
    const vectorResults = await prisma.$queryRaw`
      SELECT
        id,
        name,
        "short_description" as "shortDescription",
        "logo_url" as "logoUrl",
        (
          SELECT slug
          FROM "public"."entity_types"
          WHERE id = "entity_type_id"
        ) as "entityTypeSlug"
      FROM
        "public"."entities"
      WHERE
        "status" = 'ACTIVE'
        AND "vector_embedding" IS NOT NULL
      ORDER BY
        "created_at" DESC
      LIMIT 5;
    `;
    
    console.log(`✅ Found ${vectorResults.length} entities from vector search`);
    if (vectorResults.length > 0) {
      console.log(`   First entity: ${vectorResults[0].name} (${vectorResults[0].id})`);
    }
    
    // Step 2: Test full entity fetch for CandidateEntity conversion
    console.log('\nStep 2: Testing full entity fetch...');
    
    if (vectorResults.length === 0) {
      console.log('❌ No entities found - cannot test conversion');
      return;
    }
    
    const firstEntityId = vectorResults[0].id;
    const fullEntity = await prisma.entity.findUnique({
      where: { id: firstEntityId },
      include: {
        entityType: true,
        entityCategories: {
          include: {
            category: true
          }
        },
        entityTags: {
          include: {
            tag: true
          }
        },
        entityFeatures: {
          include: {
            feature: true
          }
        }
      }
    });
    
    if (!fullEntity) {
      console.log('❌ Could not fetch full entity data');
      return;
    }
    
    console.log(`✅ Successfully fetched full entity: ${fullEntity.name}`);
    console.log(`   Categories: ${fullEntity.entityCategories?.length || 0}`);
    console.log(`   Tags: ${fullEntity.entityTags?.length || 0}`);
    console.log(`   Features: ${fullEntity.entityFeatures?.length || 0}`);
    
    // Step 3: Test CandidateEntity conversion
    console.log('\nStep 3: Testing CandidateEntity conversion...');
    
    const candidateEntity = {
      id: fullEntity.id,
      name: fullEntity.name,
      shortDescription: fullEntity.shortDescription,
      description: fullEntity.description,
      entityType: {
        name: fullEntity.entityType.name,
        slug: fullEntity.entityType.slug,
      },
      categories: fullEntity.entityCategories || [],
      tags: fullEntity.entityTags || [],
      features: fullEntity.entityFeatures || [],
      websiteUrl: fullEntity.websiteUrl,
      logoUrl: fullEntity.logoUrl,
      avgRating: fullEntity.avgRating,
      reviewCount: fullEntity.reviewCount,
    };
    
    console.log('✅ Successfully converted to CandidateEntity format');
    console.log('   Sample data:');
    console.log(`   - ID: ${candidateEntity.id}`);
    console.log(`   - Name: ${candidateEntity.name}`);
    console.log(`   - Type: ${candidateEntity.entityType.name}`);
    console.log(`   - Categories: ${candidateEntity.categories.map(c => c.category?.name || 'Unknown').join(', ')}`);
    console.log(`   - Features: ${candidateEntity.features.map(f => f.feature?.name || 'Unknown').join(', ')}`);
    
    // Step 4: Test entity validation (simulated)
    console.log('\nStep 4: Testing entity validation...');
    
    const mockDiscoveredEntities = [
      {
        id: candidateEntity.id,
        name: candidateEntity.name,
        relevanceScore: 0.9,
        reason: 'Matches user requirements for content creation'
      },
      {
        id: 'fake-id-123',
        name: 'Fake Tool',
        relevanceScore: 0.8,
        reason: 'This should be filtered out'
      }
    ];
    
    const validEntityIds = new Set([candidateEntity.id]);
    const validatedEntities = mockDiscoveredEntities.filter(entity => 
      validEntityIds.has(entity.id)
    );
    
    console.log(`✅ Validation working: ${validatedEntities.length}/${mockDiscoveredEntities.length} entities passed validation`);
    console.log(`   Valid entity: ${validatedEntities[0]?.name}`);
    console.log(`   Filtered out: ${mockDiscoveredEntities.length - validatedEntities.length} fake entities`);
    
    console.log('\n🎉 Entity Discovery Pipeline Test Complete!');
    console.log('✅ All steps working correctly');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testEntityDiscovery().catch(console.error);
