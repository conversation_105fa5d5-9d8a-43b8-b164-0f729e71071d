/**
 * Test script to verify embedding generation flow
 * This script will test the entity creation and embedding generation process
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');

// Initialize clients
const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'
});

async function testEmbeddingGeneration() {
  console.log('🚀 Starting embedding generation test...');
  
  try {
    // Test 1: Check if OpenAI API key is working
    console.log('\n📝 Test 1: Testing OpenAI API connection...');
    const testText = "This is a test for AI tool embedding generation";
    const embedding = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: testText.trim(),
    });
    
    if (embedding.data && embedding.data.length > 0 && embedding.data[0].embedding) {
      console.log('✅ OpenAI API connection successful');
      console.log(`   Embedding length: ${embedding.data[0].embedding.length}`);
    } else {
      console.log('❌ OpenAI API connection failed - no embedding returned');
      return;
    }

    // Test 2: Check recent entities without embeddings
    console.log('\n📝 Test 2: Checking entities without embeddings...');
    const entitiesWithoutEmbeddings = await prisma.entity.findMany({
      where: {
        vectorEmbedding: null,
        status: 'ACTIVE'
      },
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        createdAt: true,
        status: true
      }
    });

    console.log(`   Found ${entitiesWithoutEmbeddings.length} entities without embeddings:`);
    entitiesWithoutEmbeddings.forEach(entity => {
      console.log(`   - ${entity.name} (${entity.id}) - Created: ${entity.createdAt}`);
    });

    // Test 3: Check entities with embeddings
    console.log('\n📝 Test 3: Checking entities with embeddings...');
    const entitiesWithEmbeddings = await prisma.entity.findMany({
      where: {
        vectorEmbedding: { not: null },
        status: 'ACTIVE'
      },
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        name: true,
        createdAt: true,
        status: true
      }
    });

    console.log(`   Found ${entitiesWithEmbeddings.length} entities with embeddings:`);
    entitiesWithEmbeddings.forEach(entity => {
      console.log(`   - ${entity.name} (${entity.id}) - Created: ${entity.createdAt}`);
    });

    // Test 4: Generate embedding for an entity without one
    if (entitiesWithoutEmbeddings.length > 0) {
      console.log('\n📝 Test 4: Generating embedding for entity without one...');
      const testEntity = entitiesWithoutEmbeddings[0];
      
      // Get full entity details for embedding generation
      const fullEntity = await prisma.entity.findUnique({
        where: { id: testEntity.id },
        include: {
          entityType: true,
          entityCategories: { include: { category: true } },
          entityTags: { include: { tag: true } },
          entityFeatures: { include: { feature: true } },
          entityDetailsTool: true,
          entityDetailsCourse: true,
          entityDetailsAgency: true,
          entityDetailsContentCreator: true,
          entityDetailsCommunity: true,
          entityDetailsNewsletter: true,
          entityDetailsDataset: true,
          entityDetailsResearchPaper: true,
          entityDetailsSoftware: true,
          entityDetailsModel: true,
          entityDetailsProjectReference: true,
          entityDetailsServiceProvider: true,
          entityDetailsInvestor: true,
          entityDetailsEvent: true,
          entityDetailsJob: true,
          entityDetailsGrant: true,
          entityDetailsBounty: true,
          entityDetailsHardware: true,
          entityDetailsNews: true,
          entityDetailsBook: true,
          entityDetailsPodcast: true,
          entityDetailsPlatform: true,
        }
      });

      if (fullEntity) {
        // Generate FTS text (similar to the service method)
        let textToEmbed = `${fullEntity.name || ''}. ${
          fullEntity.shortDescription || ''
        }. ${fullEntity.description || ''}.`;

        if (fullEntity.entityCategories.length > 0) {
          textToEmbed += ` Categories: ${fullEntity.entityCategories
            .map(ec => ec.category.name)
            .join(', ')}.`;
        }
        if (fullEntity.entityTags.length > 0) {
          textToEmbed += ` Tags: ${fullEntity.entityTags
            .map(et => et.tag.name)
            .join(', ')}.`;
        }

        console.log(`   Entity: ${fullEntity.name}`);
        console.log(`   Text to embed: "${textToEmbed.substring(0, 200)}..."`);

        // Generate embedding
        const entityEmbedding = await openai.embeddings.create({
          model: 'text-embedding-3-small',
          input: textToEmbed.trim(),
        });

        if (entityEmbedding.data && entityEmbedding.data.length > 0 && entityEmbedding.data[0].embedding) {
          const vectorString = `[${entityEmbedding.data[0].embedding.join(',')}]`;
          
          // Update the entity with the embedding
          await prisma.$executeRaw`UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${fullEntity.id}::uuid`;
          
          console.log('✅ Successfully generated and saved embedding');
          console.log(`   Embedding length: ${entityEmbedding.data[0].embedding.length}`);
        } else {
          console.log('❌ Failed to generate embedding');
        }
      }
    }

    // Test 5: Check if the embedding was saved
    console.log('\n📝 Test 5: Verifying embedding was saved...');
    const updatedEntitiesWithEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: { not: null },
        status: 'ACTIVE'
      }
    });
    console.log(`   Total entities with embeddings: ${updatedEntitiesWithEmbeddings}`);

    console.log('\n🎉 Embedding generation test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testEmbeddingGeneration();
