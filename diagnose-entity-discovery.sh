#!/bin/bash

# Comprehensive entity discovery diagnostic tool
# Tests vector search, keyword search, and entity availability

BASE_URL="https://ai-nav.onrender.com"
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3717khy7yMNKYgKQtkBr7nnSjyiuOJ3xLpWI9p5CaEg"

echo "🔍 ENTITY DISCOVERY DIAGNOSTIC SUITE"
echo "===================================="
echo ""

# Test 1: Check if entities exist in database
echo "📊 TEST 1: Entity Database Check"
echo "--------------------------------"
echo "🔍 Checking total entities..."

response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" "$BASE_URL/entities?limit=5")
total_entities=$(echo "$response" | grep -o '"total":[0-9]*' | cut -d':' -f2)
echo "📈 Total entities in database: $total_entities"

if [ "$total_entities" -gt 0 ]; then
    echo "✅ Entities exist in database"
    echo "📝 Sample entities:"
    echo "$response" | grep -o '"name":"[^"]*"' | head -3
else
    echo "❌ No entities found in database!"
fi
echo ""

# Test 2: Vector Search Direct Test
echo "📊 TEST 2: Vector Search Direct Test"
echo "------------------------------------"

test_vector_search() {
    local query="$1"
    echo "🔍 Testing vector search for: '$query'"
    
    local payload=$(cat <<EOF
{
  "query": "$query",
  "limit": 5
}
EOF
)
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$payload" \
        "$BASE_URL/entities/vector-search")
    
    local count=$(echo "$response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
    echo "📊 Vector search results: $count entities"
    
    if [ "$count" -gt 0 ]; then
        echo "✅ Vector search working"
        echo "📝 Sample results:"
        echo "$response" | grep -o '"name":"[^"]*"' | head -3
    else
        echo "❌ Vector search returned no results"
        echo "📝 Response: $response"
    fi
    echo ""
}

# Test vector search with different queries
test_vector_search "ChatGPT"
test_vector_search "video generation"
test_vector_search "spreadsheet"

# Test 3: Regular Entity Search (Keyword)
echo "📊 TEST 3: Keyword Search Test"
echo "------------------------------"

test_keyword_search() {
    local query="$1"
    echo "🔍 Testing keyword search for: '$query'"
    
    local response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "$BASE_URL/entities?searchTerm=$query&limit=5")
    
    local count=$(echo "$response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
    echo "📊 Keyword search results: $count entities"
    
    if [ "$count" -gt 0 ]; then
        echo "✅ Keyword search working"
        echo "📝 Sample results:"
        echo "$response" | grep -o '"name":"[^"]*"' | head -3
    else
        echo "❌ Keyword search returned no results"
    fi
    echo ""
}

# Test keyword search
test_keyword_search "ChatGPT"
test_keyword_search "video"
test_keyword_search "AI"

# Test 4: Entity Types Check
echo "📊 TEST 4: Entity Types Check"
echo "-----------------------------"
echo "🔍 Checking available entity types..."

response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" "$BASE_URL/entity-types")
types_count=$(echo "$response" | grep -o '"name":"[^"]*"' | wc -l | tr -d ' ')
echo "📊 Available entity types: $types_count"

if [ "$types_count" -gt 0 ]; then
    echo "✅ Entity types configured"
    echo "📝 Available types:"
    echo "$response" | grep -o '"name":"[^"]*"' | head -5
else
    echo "❌ No entity types found"
fi
echo ""

# Test 5: Recommendations Endpoint (for comparison)
echo "📊 TEST 5: Recommendations Endpoint Test"
echo "----------------------------------------"
echo "🔍 Testing recommendations endpoint for comparison..."

payload=$(cat <<EOF
{
  "problem_description": "I need an AI tool for video generation",
  "max_recommendations": 3
}
EOF
)

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d "$payload" \
    "$BASE_URL/recommendations")

rec_count=$(echo "$response" | grep -o '"entity_id":"[^"]*"' | wc -l | tr -d ' ')
echo "📊 Recommendations returned: $rec_count entities"

if [ "$rec_count" -gt 0 ]; then
    echo "✅ Recommendations endpoint working"
    echo "📝 Sample recommendations:"
    echo "$response" | grep -o '"entity_name":"[^"]*"' | head -3
else
    echo "❌ Recommendations endpoint returned no results"
    echo "📝 Response: $response"
fi
echo ""

echo "🎯 DIAGNOSTIC SUMMARY"
echo "====================="
echo "1. Database entities: $total_entities"
echo "2. Vector search: Check individual test results above"
echo "3. Keyword search: Check individual test results above"
echo "4. Entity types: $types_count"
echo "5. Recommendations: $rec_count"
echo ""
echo "💡 If vector search fails but keyword search works, the issue is with embeddings"
echo "💡 If both fail but entities exist, the issue is with search implementation"
echo "💡 If recommendations work but chat doesn't, the issue is in chat entity discovery"
