#!/bin/bash

# Test script for chat system fixes using curl
# Tests the three representative queries to identify where tools disappear

BASE_URL="https://ai-nav.onrender.com"
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3717khy7yMNKYgKQtkBr7nnSjyiuOJ3xLpWI9p5CaEg"

# Test queries
QUERIES=(
  "teacher?"
  "spreadsheet free trial?"
  "cheapest video generator"
)

echo "🚀 STARTING CHAT SYSTEM DIAGNOSTIC TESTS"
echo "📍 Base URL: $BASE_URL"
echo "🔑 Auth Token: ${JWT_TOKEN:0:20}..."
echo ""

# Function to test a single query
test_query() {
  local query="$1"
  local test_num="$2"
  
  echo "================================================================================"
  echo "🧪 TEST $test_num: \"$query\""
  echo "================================================================================"
  
  # Create JSON payload
  local payload=$(cat <<EOF
{
  "message": "$query"
}
EOF
)
  
  echo "📤 Sending request..."
  
  # Make the request and capture response
  local response=$(curl -s -w "\n%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d "$payload" \
    "$BASE_URL/chat")
  
  # Extract HTTP status code (last line)
  local http_code=$(echo "$response" | tail -n1)
  local response_body=$(echo "$response" | head -n -1)
  
  echo "📊 HTTP Status: $http_code"
  
  if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
    echo "✅ SUCCESS: Request completed"
    echo ""
    echo "📝 Response Body:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"
    echo ""
    
    # Extract key metrics
    local message=$(echo "$response_body" | grep -o '"message":"[^"]*"' | cut -d'"' -f4)
    local entities_count=$(echo "$response_body" | grep -o '"discovered_entities":\[[^]]*\]' | grep -o '{"id"' | wc -l | tr -d ' ')
    local session_id=$(echo "$response_body" | grep -o '"session_id":"[^"]*"' | cut -d'"' -f4)
    local stage=$(echo "$response_body" | grep -o '"conversation_stage":"[^"]*"' | cut -d'"' -f4)
    
    echo "📊 KEY METRICS:"
    echo "   💬 Message Preview: ${message:0:100}..."
    echo "   🎯 Session ID: $session_id"
    echo "   📈 Stage: $stage"
    echo "   🔍 Entities Found: $entities_count"
    
    # Check for fallback message
    if echo "$message" | grep -q "I apologize, but I'm having trouble"; then
      echo "   ❌ FALLBACK MESSAGE DETECTED!"
    else
      echo "   ✅ No fallback message"
    fi
    
  else
    echo "❌ FAILED: HTTP $http_code"
    echo "📝 Error Response:"
    echo "$response_body"
  fi
  
  echo ""
  echo "⏱️  Waiting 3 seconds before next test..."
  sleep 3
  echo ""
}

# Check if server is running
echo "🔍 Checking if server is running..."
if curl -s "$BASE_URL" >/dev/null 2>&1; then
  echo "✅ Server is running"
else
  echo "❌ Server is not responding at $BASE_URL"
  echo "   Continuing anyway to test..."
fi

echo ""

# Run tests
for i in "${!QUERIES[@]}"; do
  test_query "${QUERIES[$i]}" $((i+1))
done

echo "================================================================================"
echo "📊 TEST SUMMARY COMPLETE"
echo "================================================================================"
echo ""
echo "🔍 DIAGNOSTIC CHECKLIST:"
echo "1. ✅ Check server logs for entity discovery results (candidateEntities.length)"
echo "2. ✅ Look for prompt size logs (should be > 2KB)"
echo "3. ✅ Verify LLM response parsing logs"
echo "4. ✅ Check for fallback to keyword search if vector search fails"
echo "5. ✅ Ensure no 'I apologize, but I'm having trouble...' responses"
echo ""
echo "🎯 If you see fallback messages, check the server logs for diagnostic output!"
