#!/usr/bin/env node

/**
 * Direct OpenAI API test to debug the LLM provider issue
 */

const OpenAI = require('openai');

async function testOpenAIDirectly() {
  console.log('🔧 TESTING OPENAI API DIRECTLY\n');

  const apiKey = '********************************************************************************************************************************************************************';
  
  if (!apiKey) {
    console.log('❌ No OpenAI API key found');
    return;
  }

  console.log('✅ API Key found (length:', apiKey.length, ')');

  const openai = new OpenAI({ apiKey });

  try {
    console.log('\n📝 Testing simple completion...');
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'user',
          content: 'Say "Hello from OpenAI!" and nothing else.'
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    console.log('✅ OpenAI API call successful!');
    console.log('Response:', response.choices[0].message.content);
    console.log('Usage:', response.usage);

    // Test a more complex prompt similar to what the chat system uses
    console.log('\n📝 Testing chat-style prompt...');
    
    const chatResponse = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: 'You are an AI assistant that helps users find AI tools. Respond with a JSON object containing a "message" field with your response.'
        },
        {
          role: 'user',
          content: 'I need a tool for removing backgrounds from images'
        }
      ],
      max_tokens: 100,
      temperature: 0.7
    });

    console.log('✅ Chat-style prompt successful!');
    console.log('Response:', chatResponse.choices[0].message.content);

    return true;

  } catch (error) {
    console.log('❌ OpenAI API Error:');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
    
    if (error.response) {
      console.log('HTTP Status:', error.response.status);
      console.log('Response data:', error.response.data);
    }
    
    if (error.code) {
      console.log('Error code:', error.code);
    }

    return false;
  }
}

async function main() {
  const success = await testOpenAIDirectly();
  
  if (success) {
    console.log('\n🎉 OpenAI API is working correctly!');
    console.log('The issue must be in the application layer.');
    console.log('\n🔧 Next steps:');
    console.log('1. Check application error logs');
    console.log('2. Verify OpenAI service configuration');
    console.log('3. Test the OpenAI service within the app');
  } else {
    console.log('\n🔧 OpenAI API is failing!');
    console.log('This explains why the chat system uses OPENAI_FALLBACK.');
    console.log('\n🔧 Next steps:');
    console.log('1. Check API key validity');
    console.log('2. Verify billing/credits');
    console.log('3. Check rate limits');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
