/**
 * Quick script to check the current state of entity embeddings
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkEmbeddingStatus() {
  console.log('🔍 Checking entity embedding status...\n');
  
  try {
    // Get total entity counts
    const totalEntities = await prisma.entity.count();
    const activeEntities = await prisma.entity.count({
      where: { status: 'ACTIVE' }
    });
    const pendingEntities = await prisma.entity.count({
      where: { status: 'PENDING' }
    });

    console.log('📊 Entity Status Overview:');
    console.log(`   Total entities: ${totalEntities}`);
    console.log(`   Active entities: ${activeEntities}`);
    console.log(`   Pending entities: ${pendingEntities}`);

    // Check embedding status
    const entitiesWithEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: { not: null }
      }
    });

    const entitiesWithoutEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: null
      }
    });

    const activeWithoutEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: null,
        status: 'ACTIVE'
      }
    });

    console.log('\n🎯 Embedding Status:');
    console.log(`   Entities with embeddings: ${entitiesWithEmbeddings}`);
    console.log(`   Entities without embeddings: ${entitiesWithoutEmbeddings}`);
    console.log(`   Active entities without embeddings: ${activeWithoutEmbeddings}`);

    // Show recent entities without embeddings
    if (activeWithoutEmbeddings > 0) {
      console.log('\n❌ Recent active entities without embeddings:');
      const recentWithoutEmbeddings = await prisma.entity.findMany({
        where: {
          vectorEmbedding: null,
          status: 'ACTIVE'
        },
        take: 10,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          name: true,
          shortDescription: true,
          createdAt: true,
          entityType: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      });

      recentWithoutEmbeddings.forEach((entity, index) => {
        console.log(`   ${index + 1}. ${entity.name} (${entity.entityType.name})`);
        console.log(`      ID: ${entity.id}`);
        console.log(`      Created: ${entity.createdAt}`);
        console.log(`      Description: ${entity.shortDescription?.substring(0, 100)}...`);
        console.log('');
      });
    }

    // Show recent entities with embeddings
    if (entitiesWithEmbeddings > 0) {
      console.log('\n✅ Recent entities with embeddings:');
      const recentWithEmbeddings = await prisma.entity.findMany({
        where: {
          vectorEmbedding: { not: null }
        },
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          name: true,
          createdAt: true,
          entityType: {
            select: {
              name: true,
              slug: true
            }
          }
        }
      });

      recentWithEmbeddings.forEach((entity, index) => {
        console.log(`   ${index + 1}. ${entity.name} (${entity.entityType.name})`);
        console.log(`      Created: ${entity.createdAt}`);
        console.log('');
      });
    }

    // Calculate embedding coverage percentage
    const embeddingCoverage = totalEntities > 0 ? ((entitiesWithEmbeddings / totalEntities) * 100).toFixed(1) : 0;
    const activeCoverage = activeEntities > 0 ? (((activeEntities - activeWithoutEmbeddings) / activeEntities) * 100).toFixed(1) : 0;

    console.log('\n📈 Coverage Statistics:');
    console.log(`   Overall embedding coverage: ${embeddingCoverage}% (${entitiesWithEmbeddings}/${totalEntities})`);
    console.log(`   Active entity coverage: ${activeCoverage}% (${activeEntities - activeWithoutEmbeddings}/${activeEntities})`);

    if (activeWithoutEmbeddings > 0) {
      console.log('\n⚠️  ISSUE DETECTED: Active entities without embeddings found!');
      console.log('   This indicates that the embedding generation process may not be working correctly.');
      console.log('   New entities should automatically get embeddings during creation.');
    } else {
      console.log('\n✅ All active entities have embeddings - system is working correctly!');
    }

  } catch (error) {
    console.error('❌ Error checking embedding status:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkEmbeddingStatus();
