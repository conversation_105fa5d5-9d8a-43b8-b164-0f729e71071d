/**
 * Comprehensive test for the enhanced chat system
 * Tests the complete flow with enhanced Anthropic LLM service
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

// Test configuration
const TEST_CONFIG = {
  baseUrl: BASE_URL,
  timeout: 30000,
  maxRetries: 3,
};

// Test scenarios to verify enhanced chat functionality
const TEST_SCENARIOS = [
  {
    name: 'Initial Greeting - Context Awareness',
    message: 'Hello, I need help finding AI tools',
    expectedBehaviors: [
      'Should not mention conversation history issues',
      'Should provide engaging greeting',
      'Should ask about specific needs',
      'Should not use generic fallback responses'
    ]
  },
  {
    name: 'Specific Use Case - Content Creation',
    message: 'I need AI tools for creating marketing content and social media posts',
    expectedBehaviors: [
      'Should understand content creation context',
      'Should suggest relevant tools',
      'Should ask follow-up questions about specific needs',
      'Should maintain conversation context'
    ]
  },
  {
    name: 'Repetition Test - Same Question',
    message: 'I need AI tools for creating marketing content and social media posts',
    expectedBehaviors: [
      'Should detect repetition',
      'Should acknowledge the repeated question',
      'Should provide different perspective or ask for clarification',
      'Should NOT give identical response'
    ]
  },
  {
    name: 'Follow-up Question - Budget Inquiry',
    message: 'What are some free options?',
    expectedBehaviors: [
      'Should remember previous context about content creation',
      'Should focus on free tools',
      'Should maintain conversation flow',
      'Should not ask about use case again'
    ]
  },
  {
    name: 'Refinement - Specific Features',
    message: 'I specifically need tools that can generate images and write captions',
    expectedBehaviors: [
      'Should understand specific feature requirements',
      'Should suggest tools with both capabilities',
      'Should build on previous conversation',
      'Should move toward recommendations'
    ]
  }
];

class EnhancedChatTester {
  constructor() {
    this.sessionId = null;
    this.conversationHistory = [];
    this.testResults = [];
  }

  async runAllTests() {
    console.log('🚀 Starting Enhanced Chat System Tests');
    console.log('=' .repeat(60));

    try {
      // Initialize session
      await this.initializeSession();
      
      // Run test scenarios sequentially
      for (const scenario of TEST_SCENARIOS) {
        await this.runScenario(scenario);
        await this.delay(1000); // Brief pause between tests
      }

      // Generate test report
      this.generateReport();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async initializeSession() {
    console.log('🔧 Initializing test session...');
    
    const response = await this.makeRequest('/chat', {
      message: 'Hello',
      sessionId: null
    });

    if (response.sessionId) {
      this.sessionId = response.sessionId;
      console.log(`✅ Session initialized: ${this.sessionId}`);
    } else {
      throw new Error('Failed to initialize session');
    }
  }

  async runScenario(scenario) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    console.log('-'.repeat(40));

    try {
      const startTime = Date.now();
      
      const response = await this.makeRequest('/chat', {
        message: scenario.message,
        sessionId: this.sessionId
      });

      const responseTime = Date.now() - startTime;
      
      // Store conversation history
      this.conversationHistory.push({
        userMessage: scenario.message,
        aiResponse: response.message,
        timestamp: new Date().toISOString(),
        responseTime
      });

      // Analyze response
      const analysis = this.analyzeResponse(response, scenario);
      
      // Store test result
      this.testResults.push({
        scenario: scenario.name,
        success: analysis.success,
        analysis,
        response,
        responseTime
      });

      // Display results
      this.displayScenarioResult(scenario, analysis, response, responseTime);

    } catch (error) {
      console.error(`❌ Scenario failed: ${error.message}`);
      this.testResults.push({
        scenario: scenario.name,
        success: false,
        error: error.message
      });
    }
  }

  analyzeResponse(response, scenario) {
    const analysis = {
      success: true,
      issues: [],
      strengths: [],
      score: 0
    };

    const message = response.message?.toLowerCase() || '';

    // Check for problematic phrases
    const problematicPhrases = [
      'i apologize, but i\'m having trouble accessing',
      'i don\'t have access to our conversation history',
      'let\'s start fresh',
      'i can\'t see our previous conversation'
    ];

    problematicPhrases.forEach(phrase => {
      if (message.includes(phrase)) {
        analysis.issues.push(`Contains problematic phrase: "${phrase}"`);
        analysis.success = false;
      }
    });

    // Check response quality
    if (message.length < 50) {
      analysis.issues.push('Response too short (less than 50 characters)');
    } else {
      analysis.strengths.push('Adequate response length');
      analysis.score += 20;
    }

    if (response.intent) {
      analysis.strengths.push('Intent classification present');
      analysis.score += 20;
    }

    if (response.followUpQuestions && response.followUpQuestions.length > 0) {
      analysis.strengths.push('Follow-up questions provided');
      analysis.score += 20;
    }

    if (response.discoveredEntities && response.discoveredEntities.length > 0) {
      analysis.strengths.push('Entity discovery working');
      analysis.score += 20;
    }

    // Context awareness check
    if (this.conversationHistory.length > 1) {
      const previousMessages = this.conversationHistory.slice(0, -1);
      const hasContextReference = previousMessages.some(prev => 
        message.includes(prev.userMessage.toLowerCase().split(' ')[0]) ||
        message.includes('mentioned') ||
        message.includes('discussed') ||
        message.includes('earlier')
      );
      
      if (hasContextReference) {
        analysis.strengths.push('Shows context awareness');
        analysis.score += 20;
      } else {
        analysis.issues.push('Limited context awareness');
      }
    }

    // Repetition handling (for repetition test)
    if (scenario.name.includes('Repetition')) {
      const isIdenticalToPrevious = this.conversationHistory.length > 1 && 
        this.conversationHistory[this.conversationHistory.length - 1].aiResponse === 
        this.conversationHistory[this.conversationHistory.length - 2].aiResponse;
      
      if (isIdenticalToPrevious) {
        analysis.issues.push('Identical response to repeated question');
        analysis.success = false;
      } else {
        analysis.strengths.push('Handled repetition appropriately');
        analysis.score += 20;
      }
    }

    return analysis;
  }

  displayScenarioResult(scenario, analysis, response, responseTime) {
    const status = analysis.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} (${responseTime}ms)`);
    
    console.log(`\n📝 User: "${scenario.message}"`);
    console.log(`🤖 AI: "${response.message?.substring(0, 200)}${response.message?.length > 200 ? '...' : ''}"`);
    
    if (analysis.strengths.length > 0) {
      console.log(`\n✅ Strengths:`);
      analysis.strengths.forEach(strength => console.log(`   • ${strength}`));
    }
    
    if (analysis.issues.length > 0) {
      console.log(`\n⚠️  Issues:`);
      analysis.issues.forEach(issue => console.log(`   • ${issue}`));
    }

    console.log(`\n📊 Quality Score: ${analysis.score}/100`);
    
    if (response.intent) {
      console.log(`🎯 Intent: ${response.intent.type} (${response.intent.confidence})`);
    }
    
    if (response.followUpQuestions && response.followUpQuestions.length > 0) {
      console.log(`❓ Follow-ups: ${response.followUpQuestions.length} questions`);
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 ENHANCED CHAT SYSTEM TEST REPORT');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);

    console.log(`\n📈 Overall Results:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Success Rate: ${successRate}%`);

    const avgResponseTime = this.testResults
      .filter(r => r.responseTime)
      .reduce((sum, r) => sum + r.responseTime, 0) / 
      this.testResults.filter(r => r.responseTime).length;

    console.log(`   Average Response Time: ${avgResponseTime.toFixed(0)}ms`);

    // Detailed results
    console.log(`\n📋 Detailed Results:`);
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const score = result.analysis?.score || 0;
      console.log(`   ${index + 1}. ${status} ${result.scenario} (${score}/100)`);
    });

    // Key insights
    console.log(`\n🔍 Key Insights:`);
    
    const hasRepetitionIssues = this.testResults.some(r => 
      r.analysis?.issues?.some(issue => issue.includes('identical response'))
    );
    
    if (!hasRepetitionIssues) {
      console.log(`   ✅ Repetition handling working correctly`);
    } else {
      console.log(`   ❌ Repetition issues detected`);
    }

    const hasContextIssues = this.testResults.some(r => 
      r.analysis?.issues?.some(issue => issue.includes('conversation history'))
    );
    
    if (!hasContextIssues) {
      console.log(`   ✅ No conversation history access issues`);
    } else {
      console.log(`   ❌ Conversation history access problems detected`);
    }

    // Recommendations
    console.log(`\n💡 Recommendations:`);
    if (successRate < 80) {
      console.log(`   • Success rate below 80% - review failed test cases`);
    }
    if (avgResponseTime > 5000) {
      console.log(`   • Response time over 5s - consider performance optimization`);
    }
    if (failedTests > 0) {
      console.log(`   • Review failed scenarios for improvement opportunities`);
    }

    console.log('\n' + '='.repeat(60));
    
    if (successRate >= 80) {
      console.log('🎉 Enhanced chat system is performing well!');
    } else {
      console.log('⚠️  Enhanced chat system needs attention.');
    }
  }

  async makeRequest(endpoint, data) {
    const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      timeout: TEST_CONFIG.timeout,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the tests
async function main() {
  const tester = new EnhancedChatTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { EnhancedChatTester };
