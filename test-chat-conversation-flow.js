#!/usr/bin/env node

/**
 * Test script to verify conversation flow and identify the exact issue
 */

const { PrismaClient } = require('./generated/prisma');

async function testConversationFlow() {
  const prisma = new PrismaClient();
  
  console.log('🧪 TESTING CONVERSATION FLOW...\n');
  
  try {
    await prisma.$connect();
    
    // Test 1: Create a conversation session
    const testSessionId = 'test_conversation_' + Date.now();
    const testUserId = '00000000-0000-0000-0000-000000000001';
    
    console.log('1️⃣ Creating test conversation session...');
    
    const testContext = {
      sessionId: testSessionId,
      userId: testUserId,
      messages: [
        { 
          id: 'msg1',
          role: 'user', 
          content: 'What is the best AI tool for content creation?',
          timestamp: new Date().toISOString()
        },
        { 
          id: 'msg2',
          role: 'assistant', 
          content: 'I\'d love to help you find the perfect AI tool for content creation! There are several excellent options depending on your specific needs.',
          timestamp: new Date().toISOString()
        }
      ],
      discoveredEntities: [],
      userPreferences: {},
      conversationStage: 'discovery',
      metadata: {
        startedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        totalMessages: 2,
        entitiesShown: []
      }
    };
    
    await prisma.conversationSession.create({
      data: {
        sessionId: testSessionId,
        userId: testUserId,
        contextData: testContext,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 3600000)
      }
    });
    
    console.log('✅ Test conversation created');
    
    // Test 2: Retrieve and verify conversation
    console.log('\n2️⃣ Retrieving conversation...');
    
    const retrieved = await prisma.conversationSession.findFirst({
      where: { sessionId: testSessionId }
    });
    
    if (retrieved) {
      console.log('✅ Conversation retrieved successfully');
      console.log('📊 Context Analysis:');
      console.log('   - Session ID:', retrieved.sessionId);
      console.log('   - User ID:', retrieved.userId);
      console.log('   - Messages Count:', retrieved.contextData?.messages?.length || 0);
      console.log('   - Conversation Stage:', retrieved.contextData?.conversationStage);
      
      // Test 3: Simulate adding a repeated message
      console.log('\n3️⃣ Simulating repeated user message...');
      
      const updatedContext = { ...retrieved.contextData };
      updatedContext.messages.push({
        id: 'msg3',
        role: 'user',
        content: 'What is the best AI tool for content creation?', // Same question
        timestamp: new Date().toISOString()
      });
      updatedContext.metadata.totalMessages = updatedContext.messages.length;
      updatedContext.metadata.lastActiveAt = new Date().toISOString();
      
      await prisma.conversationSession.update({
        where: { sessionId: testSessionId },
        data: {
          contextData: updatedContext,
          updatedAt: new Date()
        }
      });
      
      console.log('✅ Added repeated message');
      
      // Test 4: Check conversation history building
      console.log('\n4️⃣ Testing conversation history building...');
      
      const finalRetrieved = await prisma.conversationSession.findFirst({
        where: { sessionId: testSessionId }
      });
      
      if (finalRetrieved) {
        const messages = finalRetrieved.contextData.messages;
        console.log('📝 Conversation History:');
        messages.forEach((msg, index) => {
          console.log(`   ${index + 1}. ${msg.role}: ${msg.content}`);
        });
        
        // Check for repeated questions
        const userMessages = messages.filter(m => m.role === 'user');
        const repeatedQuestions = userMessages.filter((msg, index, arr) => 
          arr.findIndex(m => m.content.toLowerCase() === msg.content.toLowerCase()) !== index
        );
        
        console.log('\n🔍 Repetition Analysis:');
        console.log('   - Total user messages:', userMessages.length);
        console.log('   - Repeated questions:', repeatedQuestions.length);
        
        if (repeatedQuestions.length > 0) {
          console.log('   ⚠️  FOUND REPEATED QUESTIONS:');
          repeatedQuestions.forEach(msg => {
            console.log(`      - "${msg.content}"`);
          });
        }
        
        // Test 5: Simulate LLM context building
        console.log('\n5️⃣ Testing LLM context building...');
        
        const conversationHistory = messages.slice(-10).map(msg => `${msg.role}: ${msg.content}`).join('\n');
        const currentMessage = 'What is the best AI tool for content creation?';
        
        console.log('📤 LLM Context that would be sent:');
        console.log('   Current message:', currentMessage);
        console.log('   Conversation history length:', conversationHistory.length);
        console.log('   Full history:');
        console.log('   ' + conversationHistory.replace(/\n/g, '\n   '));
        
        // Check if the context shows the repetition
        const isRepeatedInHistory = conversationHistory.toLowerCase().includes(currentMessage.toLowerCase());
        console.log('\n🔍 Context Analysis:');
        console.log('   - Is current message in history?', isRepeatedInHistory);
        console.log('   - Should AI detect repetition?', isRepeatedInHistory && repeatedQuestions.length > 0);
      }
    }
    
    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await prisma.conversationSession.delete({
      where: { sessionId: testSessionId }
    });
    console.log('✅ Test data cleaned up');
    
  } catch (error) {
    console.log('❌ ERROR:', error.message);
    console.log('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  console.log('\n🏁 CONVERSATION FLOW TEST COMPLETE');
}

// Run the test
testConversationFlow().catch(console.error);
