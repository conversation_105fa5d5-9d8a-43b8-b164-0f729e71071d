#!/usr/bin/env node

/**
 * Test script to verify LLM health check bypass functionality
 */

const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

async function testHealthBypass() {
  console.log('🧪 Testing LLM Health Check Bypass...\n');

  try {
    // Test 1: Check if health bypass is working by making a chat request
    console.log('1. Testing chat endpoint with health bypass...');
    
    const chatResponse = await axios.post(`${BASE_URL}/chat`, {
      message: "background remover",
      sessionId: "test-session-" + Date.now()
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    console.log('✅ Chat endpoint responded successfully');
    console.log('Response metadata:', {
      llm_provider: chatResponse.data.metadata?.llm_provider,
      conversation_stage: chatResponse.data.metadata?.conversation_stage,
      entities_found: chatResponse.data.metadata?.entities_found
    });

    // Test 2: Check if we get actual recommendations
    if (chatResponse.data.message && chatResponse.data.message.toLowerCase().includes('designify')) {
      console.log('✅ Found expected tool recommendation (Designify)');
    } else {
      console.log('⚠️  Expected tool recommendation not found in response');
      console.log('Response message preview:', chatResponse.data.message?.substring(0, 200) + '...');
    }

    // Test 3: Test admin health endpoint if available
    console.log('\n2. Testing admin health endpoint...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/admin/settings/llm/health`, {
        headers: {
          'Authorization': `Bearer ${process.env.ADMIN_TOKEN || 'test-token'}`
        }
      });
      console.log('✅ Admin health endpoint accessible');
      console.log('Provider health status:', healthResponse.data.status);
    } catch (error) {
      console.log('⚠️  Admin health endpoint not accessible (expected if not authenticated)');
    }

    console.log('\n🎉 Health bypass test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    process.exit(1);
  }
}

// Set bypass flag for testing
process.env.LLM_HEALTH_CHECK_DISABLE = 'true';
console.log('🔧 Set LLM_HEALTH_CHECK_DISABLE=true for testing\n');

testHealthBypass();
