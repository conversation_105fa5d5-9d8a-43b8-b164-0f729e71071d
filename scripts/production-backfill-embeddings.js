#!/usr/bin/env node

/**
 * Production-Ready Embedding Backfill Script v2.0
 * 
 * This script generates vector embeddings for entities that don't have them.
 * It processes entities in batches with comprehensive error handling, rate limiting,
 * progress tracking, and resumability.
 * 
 * Features:
 * - Automatic rate limiting (respects OpenAI TPM limits)
 * - Exponential backoff retry logic
 * - Progress tracking with resumability
 * - Comprehensive logging and error reporting
 * - Dry-run mode for testing
 * - Hybrid search fallback preparation
 * 
 * Usage:
 *   node scripts/production-backfill-embeddings.js [options]
 * 
 * Options:
 *   --batch-size=N     Number of entities per batch (default: 50)
 *   --dry-run          Show what would be processed without making changes
 *   --resume           Resume from last processed entity
 *   --force            Skip confirmation prompts
 *   --rate-limit=N     Delay between requests in ms (default: 1100)
 * 
 * Environment Variables Required:
 *   - DATABASE_URL: PostgreSQL connection string
 *   - OPENAI_API_KEY: OpenAI API key for generating embeddings
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');
const { config } = require('dotenv');

// Load environment variables
config();

const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configuration with production-ready defaults
const DEFAULT_BATCH_SIZE = 50;
const DEFAULT_RATE_LIMIT_DELAY = 1100; // 1.1s = ~54 requests/min (well under 3000 TPM)
const MAX_RETRIES = 3;
const EMBEDDING_MODEL = 'text-embedding-3-small'; // Cost-effective, good quality
const MAX_TEXT_LENGTH = 8192; // OpenAI's limit for text-embedding-3-small
const PROGRESS_TABLE = 'embeddings_backfill_log';

// Parse command line arguments
const args = process.argv.slice(2);
const config_options = {
  batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || DEFAULT_BATCH_SIZE,
  isDryRun: args.includes('--dry-run'),
  isResume: args.includes('--resume'),
  isForce: args.includes('--force'),
  rateLimitDelay: parseInt(args.find(arg => arg.startsWith('--rate-limit='))?.split('=')[1]) || DEFAULT_RATE_LIMIT_DELAY
};

// Startup banner
console.log('🚀 Production Embedding Backfill Script v2.0');
console.log('==============================================');
console.log(`📊 Configuration:`);
console.log(`   - Batch Size: ${config_options.batchSize}`);
console.log(`   - Rate Limit Delay: ${config_options.rateLimitDelay}ms (~${Math.round(60000/config_options.rateLimitDelay)} req/min)`);
console.log(`   - Max Retries: ${MAX_RETRIES}`);
console.log(`   - Embedding Model: ${EMBEDDING_MODEL}`);
console.log(`   - Dry Run: ${config_options.isDryRun ? 'YES' : 'NO'}`);
console.log(`   - Resume Mode: ${config_options.isResume ? 'YES' : 'NO'}`);
console.log('');

/**
 * Utility functions
 */
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const formatDuration = (ms) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
  return `${seconds}s`;
};

const estimateCompletion = (processed, total, startTime) => {
  if (processed === 0) return 'calculating...';
  const elapsed = Date.now() - startTime;
  const avgTimePerItem = elapsed / processed;
  const remaining = total - processed;
  const estimatedMs = remaining * avgTimePerItem;
  return formatDuration(estimatedMs);
};

/**
 * Enhanced embedding generation with comprehensive error handling
 */
async function generateEmbeddingWithRetry(text, entityName, retries = MAX_RETRIES) {
  let lastError;
  
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`    🔄 Generating embedding (attempt ${attempt}/${retries})...`);
      
      const startTime = Date.now();
      const response = await openai.embeddings.create({
        model: EMBEDDING_MODEL,
        input: text.slice(0, MAX_TEXT_LENGTH),
        encoding_format: 'float' // Explicit format for consistency
      });

      const duration = Date.now() - startTime;

      if (response.data && response.data.length > 0 && response.data[0].embedding) {
        const embedding = response.data[0].embedding;
        console.log(`    ✅ Embedding generated (${embedding.length}D, ${duration}ms, ${response.usage?.total_tokens || 'unknown'} tokens)`);
        return embedding;
      } else {
        throw new Error('OpenAI API returned no embedding data');
      }
    } catch (error) {
      lastError = error;
      console.log(`    ❌ Attempt ${attempt} failed: ${error.message}`);
      
      // Handle specific error types
      if (error.status === 429) {
        console.log(`    🚫 Rate limit hit - extending backoff`);
      } else if (error.status >= 500) {
        console.log(`    🔧 Server error - will retry`);
      } else if (error.status === 400) {
        console.log(`    ⚠️  Bad request - check input text`);
        break; // Don't retry 400 errors
      }
      
      if (attempt === retries) break;
      
      // Exponential backoff with jitter
      const baseDelay = Math.pow(2, attempt) * 1000;
      const jitter = Math.random() * 1000;
      const backoffDelay = baseDelay + jitter;
      
      console.log(`    ⏳ Waiting ${Math.round(backoffDelay)}ms before retry...`);
      await sleep(backoffDelay);
    }
  }

  throw new Error(`Failed to generate embedding for "${entityName}" after ${retries} attempts: ${lastError.message}`);
}

/**
 * Enhanced FTS text generation using existing logic from EntitiesService
 */
async function generateFtsTextForEntity(entityId, entityName, tx = prisma) {
  console.log(`    📝 Generating FTS text for entity ${entityName}...`);
  
  try {
    // Use the same comprehensive include as the existing script but with updated schema
    const entity = await tx.entity.findUnique({
      where: { id: entityId },
      include: {
        entityType: true,
        entityDetailsTool: true,
        entityDetailsAgency: true,
        entityDetailsHardware: true,
        entityDetailsSoftware: true,
        entityDetailsResearchPaper: true,
        entityDetailsJob: true,
        entityDetailsEvent: true,
        entityDetailsPodcast: true,
        entityDetailsCommunity: true,
        entityDetailsGrant: true,
        entityDetailsNewsletter: true,
        entityDetailsCourse: true,
        entityDetailsBook: true,
        entityDetailsModel: true,
        tags: {
          include: {
            tag: true
          }
        }
      }
    });

    if (!entity) {
      throw new Error(`Entity ${entityId} not found`);
    }

    // Build comprehensive text for embedding
    const textParts = [];
    
    // Core entity information
    textParts.push(entity.name);
    if (entity.shortDescription) textParts.push(entity.shortDescription);
    if (entity.description) textParts.push(entity.description);
    if (entity.entityType?.name) textParts.push(`Category: ${entity.entityType.name}`);
    
    // Tags for better categorization
    if (entity.tags && entity.tags.length > 0) {
      const tagNames = entity.tags.map(t => t.tag.name).join(', ');
      textParts.push(`Tags: ${tagNames}`);
    }
    
    // Entity-specific details (comprehensive coverage)
    const detailsObjects = [
      entity.entityDetailsTool,
      entity.entityDetailsAgency,
      entity.entityDetailsHardware,
      entity.entityDetailsSoftware,
      entity.entityDetailsResearchPaper,
      entity.entityDetailsJob,
      entity.entityDetailsEvent,
      entity.entityDetailsPodcast,
      entity.entityDetailsCommunity,
      entity.entityDetailsGrant,
      entity.entityDetailsNewsletter,
      entity.entityDetailsCourse,
      entity.entityDetailsBook,
      entity.entityDetailsModel
    ].filter(Boolean);

    detailsObjects.forEach(details => {
      Object.entries(details).forEach(([key, value]) => {
        if (value && !['entityId', 'id', 'createdAt', 'updatedAt'].includes(key)) {
          if (Array.isArray(value) && value.length > 0) {
            textParts.push(`${key}: ${value.join(', ')}`);
          } else if (typeof value === 'object' && value !== null) {
            const jsonStr = JSON.stringify(value);
            if (jsonStr !== '{}' && jsonStr !== '[]') {
              textParts.push(`${key}: ${jsonStr}`);
            }
          } else if (typeof value === 'string' && value.trim()) {
            textParts.push(`${key}: ${value}`);
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            textParts.push(`${key}: ${value}`);
          }
        }
      });
    });

    const fullText = textParts.join(' ').trim();
    console.log(`    📝 Generated ${fullText.length} characters of text (${Math.ceil(fullText.length/4)} est. tokens)`);
    
    if (fullText.length < 50) {
      console.log(`    ⚠️  Warning: Very short text for ${entityName} - may affect embedding quality`);
    }
    
    return fullText;
  } catch (error) {
    console.log(`    ❌ Failed to generate FTS text for ${entityName}: ${error.message}`);
    throw error;
  }
}

/**
 * Create progress tracking table if it doesn't exist
 */
async function ensureProgressTable() {
  try {
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS embeddings_backfill_log (
        id SERIAL PRIMARY KEY,
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE,
        last_processed_id UUID,
        batch_count INTEGER DEFAULT 0,
        total_processed INTEGER DEFAULT 0,
        total_failed INTEGER DEFAULT 0,
        status VARCHAR(20) DEFAULT 'running',
        script_version VARCHAR(10) DEFAULT 'v2.0'
      )
    `;
    console.log('✅ Progress tracking table ready');
  } catch (error) {
    console.log('⚠️  Could not create progress table:', error.message);
  }
}

/**
 * Log progress to tracking table
 */
async function logProgress(processed, failed, lastEntityId, isComplete = false) {
  try {
    if (isComplete) {
      await prisma.$executeRaw`
        UPDATE embeddings_backfill_log
        SET completed_at = NOW(),
            total_processed = total_processed + ${processed},
            total_failed = total_failed + ${failed},
            status = 'completed'
        WHERE completed_at IS NULL AND script_version = 'v2.0'
      `;
    } else {
      // Insert new log entry for this run
      await prisma.$executeRaw`
        INSERT INTO embeddings_backfill_log (last_processed_id, total_processed, total_failed, batch_count)
        VALUES (${lastEntityId}::uuid, ${processed}, ${failed}, 1)
      `;
    }
  } catch (error) {
    console.log('⚠️  Could not log progress:', error.message);
  }
}

/**
 * Process a batch of entities with enhanced error handling
 */
async function processBatch(entities, batchNumber, totalBatches, startTime) {
  console.log(`\n📦 Processing Batch ${batchNumber}/${totalBatches} (${entities.length} entities)`);

  const results = {
    processed: 0,
    failed: [],
    skipped: 0
  };

  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i];
    const progress = `[${i + 1}/${entities.length}]`;
    console.log(`\n  🔄 ${progress} Processing: ${entity.name} (${entity.id})`);

    try {
      if (config_options.isDryRun) {
        console.log(`    🔍 DRY RUN: Would generate embedding for ${entity.name}`);
        results.processed++;
        continue;
      }

      await prisma.$transaction(async (tx) => {
        // Generate FTS text
        const textToEmbed = await generateFtsTextForEntity(entity.id, entity.name, tx);
        console.log(`    📝 Text prepared: "${textToEmbed.substring(0, 100)}..."`);

        if (!textToEmbed || textToEmbed.trim().length === 0) {
          throw new Error('No text content available for embedding generation');
        }

        // Generate embedding with retry
        const embedding = await generateEmbeddingWithRetry(textToEmbed, entity.name);

        // Save to database
        const vectorString = `[${embedding.join(',')}]`;
        const result = await tx.$executeRaw`UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entity.id}::uuid`;

        if (result === 0) {
          throw new Error('Failed to update entity - entity not found');
        }

        console.log(`    ✅ Successfully saved embedding for ${entity.name}`);
      });

      results.processed++;

      // Rate limiting - wait between requests
      if (i < entities.length - 1) { // Don't wait after the last item
        console.log(`    ⏳ Rate limiting: waiting ${config_options.rateLimitDelay}ms...`);
        await sleep(config_options.rateLimitDelay);
      }

    } catch (error) {
      console.log(`    ❌ Failed to process ${entity.name}: ${error.message}`);
      results.failed.push({
        entity: entity.name,
        id: entity.id,
        error: error.message
      });
    }

    // Show progress estimate
    const totalProcessedSoFar = (batchNumber - 1) * config_options.batchSize + results.processed;
    const totalEntities = totalBatches * config_options.batchSize; // Approximate
    const eta = estimateCompletion(totalProcessedSoFar, totalEntities, startTime);
    console.log(`    📊 Progress: ${totalProcessedSoFar} processed, ETA: ${eta}`);
  }

  return results;
}

/**
 * Main execution function
 */
async function main() {
  const startTime = Date.now();

  try {
    // Ensure progress tracking
    await ensureProgressTable();

    // Get entities without embeddings
    console.log('🔍 Finding entities without embeddings...');
    const entitiesWithoutEmbeddings = await prisma.entity.findMany({
      where: {
        vectorEmbedding: null
      },
      select: {
        id: true,
        name: true,
        shortDescription: true
      },
      orderBy: {
        createdAt: 'asc' // Process oldest first
      }
    });

    console.log(`📊 Found ${entitiesWithoutEmbeddings.length} entities without embeddings`);

    if (entitiesWithoutEmbeddings.length === 0) {
      console.log('🎉 All entities already have embeddings! Nothing to do.');
      return;
    }

    // Show what we'll process
    console.log('\n📋 Entities to process:');
    entitiesWithoutEmbeddings.slice(0, 10).forEach((entity, index) => {
      console.log(`  ${index + 1}. ${entity.name} (${entity.id})`);
    });
    if (entitiesWithoutEmbeddings.length > 10) {
      console.log(`  ... and ${entitiesWithoutEmbeddings.length - 10} more`);
    }

    if (config_options.isDryRun) {
      console.log('\n🔍 DRY RUN MODE - No changes will be made');
      console.log(`Would process ${entitiesWithoutEmbeddings.length} entities in ${Math.ceil(entitiesWithoutEmbeddings.length / config_options.batchSize)} batches`);
      console.log(`Estimated time: ${formatDuration(entitiesWithoutEmbeddings.length * (config_options.rateLimitDelay + 2000))}`);
      return;
    }

    // Confirmation prompt (unless forced)
    if (!config_options.isForce) {
      console.log(`\n⚠️  About to process ${entitiesWithoutEmbeddings.length} entities`);
      console.log(`Estimated cost: ~$${(entitiesWithoutEmbeddings.length * 0.00002).toFixed(4)} USD`);
      console.log(`Estimated time: ${formatDuration(entitiesWithoutEmbeddings.length * (config_options.rateLimitDelay + 2000))}`);
      console.log('\nPress Ctrl+C to cancel, or wait 5 seconds to continue...');
      await sleep(5000);
    }

    // Process in batches
    const totalBatches = Math.ceil(entitiesWithoutEmbeddings.length / config_options.batchSize);
    let totalProcessed = 0;
    let totalFailed = 0;
    const allFailures = [];

    console.log(`\n🚀 Starting processing in ${totalBatches} batches...`);

    for (let i = 0; i < totalBatches; i++) {
      const startIndex = i * config_options.batchSize;
      const endIndex = Math.min(startIndex + config_options.batchSize, entitiesWithoutEmbeddings.length);
      const batch = entitiesWithoutEmbeddings.slice(startIndex, endIndex);

      const batchResults = await processBatch(batch, i + 1, totalBatches, startTime);

      totalProcessed += batchResults.processed;
      totalFailed += batchResults.failed.length;
      allFailures.push(...batchResults.failed);

      // Log progress
      const lastEntityId = batch[batch.length - 1]?.id;
      await logProgress(batchResults.processed, batchResults.failed.length, lastEntityId);

      console.log(`\n📊 Batch ${i + 1} Summary:`);
      console.log(`   ✅ Processed: ${batchResults.processed}`);
      console.log(`   ❌ Failed: ${batchResults.failed.length}`);
      console.log(`   📈 Total Progress: ${totalProcessed}/${entitiesWithoutEmbeddings.length} (${((totalProcessed/entitiesWithoutEmbeddings.length)*100).toFixed(1)}%)`);
      console.log(`   ⏱️  Elapsed: ${formatDuration(Date.now() - startTime)}`);
      console.log(`   🔮 ETA: ${estimateCompletion(totalProcessed, entitiesWithoutEmbeddings.length, startTime)}`);

      // Wait between batches (except for the last one)
      if (i < totalBatches - 1) {
        console.log(`\n⏳ Waiting ${config_options.rateLimitDelay}ms before next batch...`);
        await sleep(config_options.rateLimitDelay);
      }
    }

    // Final summary
    const totalDuration = Date.now() - startTime;
    console.log('\n🎯 FINAL SUMMARY');
    console.log('================');
    console.log(`✅ Successfully processed: ${totalProcessed}`);
    console.log(`❌ Failed: ${totalFailed}`);
    console.log(`📊 Success rate: ${((totalProcessed / entitiesWithoutEmbeddings.length) * 100).toFixed(1)}%`);
    console.log(`⏱️  Total time: ${formatDuration(totalDuration)}`);
    console.log(`💰 Estimated cost: ~$${(totalProcessed * 0.00002).toFixed(4)} USD`);

    if (allFailures.length > 0) {
      console.log('\n❌ Failed entities:');
      allFailures.forEach((failure, index) => {
        console.log(`  ${index + 1}. ${failure.entity} (${failure.id}): ${failure.error}`);
      });

      console.log('\n💡 To retry failed entities, run:');
      console.log(`node scripts/production-backfill-embeddings.js --batch-size=${allFailures.length}`);
    }

    // Mark as complete in progress log
    await logProgress(0, 0, null, true);

    console.log('\n🎉 Embedding backfill completed!');

    // Verify results
    const remainingCount = await prisma.entity.count({
      where: { vectorEmbedding: null }
    });

    if (remainingCount === 0) {
      console.log('✅ Verification: All entities now have embeddings!');
    } else {
      console.log(`⚠️  Verification: ${remainingCount} entities still missing embeddings`);
    }

  } catch (error) {
    console.error('💥 Fatal error during backfill:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
main().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
