#!/usr/bin/env node

/**
 * Test Chat System with Full Embedding Coverage
 * 
 * Verifies that the chat system now provides tool recommendations
 * instead of fallback messages after achieving 100% embedding coverage.
 */

require('dotenv').config();

const TEST_QUERIES = [
  "I need a tool for removing backgrounds from images",
  "What's the best free AI video generator?", 
  "Help me find a spreadsheet tool with AI features",
  "I'm a teacher looking for AI tools for education"
];

async function testChatEndpoint(query) {
  try {
    console.log(`🔍 Testing: "${query}"`);
    
    const response = await fetch('http://localhost:3000/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.TEST_JWT_TOKEN || 'test-token'}`
      },
      body: JSON.stringify({
        message: query,
        conversation_id: `test-${Date.now()}`
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Analyze response
    const hasRecommendations = data.discovered_entities && data.discovered_entities.length > 0;
    const isFallbackMessage = data.message && data.message.includes("I apologize");
    const hasDesignify = data.discovered_entities?.some(e => e.name === "Designify");
    
    console.log(`   ${hasRecommendations ? '✅' : '❌'} Recommendations: ${data.discovered_entities?.length || 0} entities`);
    console.log(`   ${!isFallbackMessage ? '✅' : '❌'} No fallback message`);
    
    if (query.includes("background") && hasDesignify) {
      console.log(`   ✅ Designify found in background removal query`);
    }
    
    if (data.discovered_entities?.length > 0) {
      console.log(`   🎯 Top recommendations: ${data.discovered_entities.slice(0, 3).map(e => e.name).join(', ')}`);
    }
    
    return {
      query,
      success: hasRecommendations && !isFallbackMessage,
      recommendationCount: data.discovered_entities?.length || 0,
      isFallback: isFallbackMessage,
      topRecommendations: data.discovered_entities?.slice(0, 3).map(e => e.name) || []
    };

  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    return {
      query,
      success: false,
      error: error.message
    };
  }
}

async function runChatTests() {
  console.log('🚀 Chat System Integration Test');
  console.log('===============================');
  console.log('Testing with 100% embedding coverage...');
  console.log('');

  const results = [];
  
  for (const query of TEST_QUERIES) {
    const result = await testChatEndpoint(query);
    results.push(result);
    console.log('');
  }

  // Summary
  const successCount = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log('📊 CHAT INTEGRATION TEST SUMMARY');
  console.log('=================================');
  console.log(`Tests Passed: ${successCount}/${totalTests}`);
  console.log(`Success Rate: ${((successCount/totalTests) * 100).toFixed(1)}%`);
  
  if (successCount === totalTests) {
    console.log('');
    console.log('🎉 CHAT SYSTEM FULLY OPERATIONAL!');
    console.log('✅ No more fallback messages');
    console.log('✅ Tool recommendations working');
    console.log('✅ Vector search integration successful');
  } else {
    console.log('');
    console.log('⚠️  Some chat tests failed. Check server logs.');
  }

  return results;
}

// Run if called directly
if (require.main === module) {
  runChatTests()
    .then(results => {
      const allPassed = results.every(r => r.success);
      process.exit(allPassed ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Chat test crashed:', error);
      process.exit(1);
    });
}

module.exports = { runChatTests, testChatEndpoint };
