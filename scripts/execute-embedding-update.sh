#!/bin/bash

# Execute the embedding update using the generated SQL
# This script applies the embedding to the Designify entity

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Executing Embedding Update for Designify${NC}"
echo -e "${BLUE}==========================================${NC}"
echo ""

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_FILE="$SCRIPT_DIR/update-designify-embedding.sql"

# Check if SQL file exists
if [ ! -f "$SQL_FILE" ]; then
    echo -e "${RED}❌ SQL file not found: $SQL_FILE${NC}"
    echo "Please run generate-designify-embedding.sh first"
    exit 1
fi

echo -e "${GREEN}✅ Found SQL update file${NC}"

# Extract just the UPDATE statement (first part of the file)
UPDATE_SQL=$(sed -n '2,1542p' "$SQL_FILE")

echo -e "${BLUE}🔄 Executing UPDATE statement...${NC}"

# Note: In a real environment, you would execute this against your database
# For now, we'll show what would be executed and provide instructions

echo -e "${YELLOW}📝 SQL to execute:${NC}"
echo "UPDATE entities"
echo "SET vector_embedding = '[embedding_array]'::vector,"
echo "    updated_at = NOW()"
echo "WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';"
echo ""

echo -e "${YELLOW}⚠️  Manual execution required:${NC}"
echo "Due to the large size of the embedding vector (1536 dimensions),"
echo "please execute the update manually using one of these methods:"
echo ""
echo "1. Using psql command line:"
echo "   psql \$DATABASE_URL -f $SQL_FILE"
echo ""
echo "2. Using Supabase dashboard:"
echo "   - Open Supabase SQL Editor"
echo "   - Copy and paste the contents of $SQL_FILE"
echo "   - Execute the query"
echo ""
echo "3. Using the application's database connection:"
echo "   - Use the existing Prisma client"
echo "   - Execute the raw SQL update"
echo ""

# Create a verification script
VERIFY_SCRIPT="$SCRIPT_DIR/verify-embedding-update.sql"
cat > "$VERIFY_SCRIPT" <<EOF
-- Verify that Designify entity now has an embedding
SELECT 
    id,
    name,
    CASE 
        WHEN vector_embedding IS NULL THEN 'MISSING'
        ELSE 'PRESENT'
    END as embedding_status,
    updated_at
FROM entities 
WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';

-- Check overall embedding status
SELECT 
    COUNT(*) as total_entities,
    COUNT(vector_embedding) as entities_with_embeddings,
    COUNT(*) - COUNT(vector_embedding) as missing_embeddings
FROM entities;
EOF

echo -e "${GREEN}📄 Verification script created: $VERIFY_SCRIPT${NC}"
echo ""
echo -e "${BLUE}🔍 After executing the update, run the verification:${NC}"
echo "psql \$DATABASE_URL -f $VERIFY_SCRIPT"
echo ""
echo -e "${GREEN}🎯 Expected result after update:${NC}"
echo "- Designify entity: embedding_status = 'PRESENT'"
echo "- Overall: missing_embeddings = 0"
echo ""
echo -e "${YELLOW}💡 Alternative: Use the Supabase API to execute the update${NC}"
