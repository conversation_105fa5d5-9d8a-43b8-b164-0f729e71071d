-- Quick Fix for Designify Entity Embedding
-- This SQL script provides a manual approach to fix the missing embedding
-- if the Node.js scripts cannot be executed immediately

-- First, let's verify the current state
SELECT 
    id,
    name,
    short_description,
    CASE 
        WHEN vector_embedding IS NULL THEN 'MISSING'
        ELSE 'PRESENT'
    END as embedding_status
FROM entities 
WHERE name = 'Designify';

-- Show the text that would be used for embedding generation
SELECT 
    id,
    name,
    CONCAT(
        name, '. ',
        COALESCE(short_description, ''), '. ',
        COALESCE(description, '')
    ) as text_for_embedding
FROM entities 
WHERE name = 'Designify';

-- Note: The actual embedding generation requires OpenAI API call
-- This would need to be done through the Node.js scripts or manually
-- 
-- Example embedding update (replace with actual embedding vector):
-- UPDATE entities 
-- SET vector_embedding = '[0.1, 0.2, 0.3, ...]'::vector,
--     updated_at = NOW()
-- WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';

-- Verification query to run after embedding is generated
SELECT 
    COUNT(*) as total_entities,
    COUNT(vector_embedding) as entities_with_embeddings,
    COUNT(*) - COUNT(vector_embedding) as missing_embeddings
FROM entities;
