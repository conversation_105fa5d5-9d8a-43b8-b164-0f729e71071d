/**
 * Direct Fix for Designify Entity Embedding
 * 
 * This script uses the exact same logic as the existing EntitiesService
 * to generate and save the missing embedding for the Designify entity.
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');
require('dotenv').config();

const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Entity ID for Designify (the one missing embedding)
const DESIGNIFY_ENTITY_ID = '083b237b-e6ff-420e-a2cf-bfd87661372c';

async function generateFtsTextForEntity(entityId, tx) {
  console.log(`📝 Generating FTS text for entity ${entityId}...`);
  
  // Use the same comprehensive include as the existing EntitiesService
  const entity = await tx.entity.findUnique({
    where: { id: entityId },
    include: {
      entityType: true,
      entityDetailsTool: true,
      entityDetailsAgency: true,
      entityDetailsHardware: true,
      entityDetailsSoftware: true,
      entityDetailsResearchPaper: true,
      entityDetailsJob: true,
      entityDetailsEvent: true,
      entityDetailsPodcast: true,
      entityDetailsCommunity: true,
      entityDetailsGrant: true,
      entityDetailsNewsletter: true,
      entityDetailsCourse: true,
      entityDetailsBook: true,
      entityDetailsModel: true,
      tags: {
        include: {
          tag: true
        }
      }
    }
  });

  if (!entity) {
    throw new Error(`Entity ${entityId} not found`);
  }

  console.log(`📋 Entity found: ${entity.name}`);

  // Build comprehensive text for embedding (same logic as EntitiesService)
  const textParts = [];
  
  // Core entity information
  textParts.push(entity.name);
  if (entity.shortDescription) textParts.push(entity.shortDescription);
  if (entity.description) textParts.push(entity.description);
  if (entity.entityType?.name) textParts.push(`Category: ${entity.entityType.name}`);
  
  // Tags for better categorization
  if (entity.tags && entity.tags.length > 0) {
    const tagNames = entity.tags.map(t => t.tag.name).join(', ');
    textParts.push(`Tags: ${tagNames}`);
  }
  
  // Entity-specific details (comprehensive coverage)
  const detailsObjects = [
    entity.entityDetailsTool,
    entity.entityDetailsAgency,
    entity.entityDetailsHardware,
    entity.entityDetailsSoftware,
    entity.entityDetailsResearchPaper,
    entity.entityDetailsJob,
    entity.entityDetailsEvent,
    entity.entityDetailsPodcast,
    entity.entityDetailsCommunity,
    entity.entityDetailsGrant,
    entity.entityDetailsNewsletter,
    entity.entityDetailsCourse,
    entity.entityDetailsBook,
    entity.entityDetailsModel
  ].filter(Boolean);

  detailsObjects.forEach(details => {
    Object.entries(details).forEach(([key, value]) => {
      if (value && !['entityId', 'id', 'createdAt', 'updatedAt'].includes(key)) {
        if (Array.isArray(value) && value.length > 0) {
          textParts.push(`${key}: ${value.join(', ')}`);
        } else if (typeof value === 'object' && value !== null) {
          const jsonStr = JSON.stringify(value);
          if (jsonStr !== '{}' && jsonStr !== '[]') {
            textParts.push(`${key}: ${jsonStr}`);
          }
        } else if (typeof value === 'string' && value.trim()) {
          textParts.push(`${key}: ${value}`);
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          textParts.push(`${key}: ${value}`);
        }
      }
    });
  });

  const fullText = textParts.join(' ').trim();
  console.log(`📝 Generated ${fullText.length} characters of text`);
  console.log(`📄 Text preview: "${fullText.substring(0, 200)}..."`);
  
  return fullText;
}

async function generateEmbeddingWithRetry(text, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 Generating embedding (attempt ${attempt}/${maxRetries})...`);
      
      const startTime = Date.now();
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text.slice(0, 8192), // Ensure we don't exceed token limits
      });

      const duration = Date.now() - startTime;

      if (response.data && response.data.length > 0 && response.data[0].embedding) {
        const embedding = response.data[0].embedding;
        console.log(`✅ Embedding generated successfully!`);
        console.log(`   - Dimensions: ${embedding.length}`);
        console.log(`   - Duration: ${duration}ms`);
        console.log(`   - Tokens used: ${response.usage?.total_tokens || 'unknown'}`);
        return embedding;
      } else {
        throw new Error('OpenAI API returned no embedding data');
      }
    } catch (error) {
      lastError = error;
      console.log(`❌ Attempt ${attempt} failed: ${error.message}`);
      
      if (attempt === maxRetries) break;
      
      // Exponential backoff
      const backoffDelay = Math.pow(2, attempt) * 1000;
      console.log(`⏳ Waiting ${backoffDelay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, backoffDelay));
    }
  }

  throw new Error(`Failed to generate embedding after ${maxRetries} attempts: ${lastError.message}`);
}

async function fixDesignifyEmbedding() {
  console.log('🚀 Direct Fix for Designify Entity Embedding');
  console.log('============================================');
  console.log(`🎯 Target Entity ID: ${DESIGNIFY_ENTITY_ID}`);
  console.log('');
  
  try {
    // Verify the entity exists and is missing embedding
    const entity = await prisma.entity.findUnique({
      where: { id: DESIGNIFY_ENTITY_ID },
      select: {
        id: true,
        name: true,
        vectorEmbedding: true
      }
    });

    if (!entity) {
      throw new Error(`Entity with ID ${DESIGNIFY_ENTITY_ID} not found`);
    }

    console.log(`📋 Entity: ${entity.name}`);
    
    if (entity.vectorEmbedding) {
      console.log('✅ Entity already has an embedding! Nothing to do.');
      return;
    }

    console.log('❌ Confirmed: Entity is missing embedding');
    console.log('');

    // Generate and save embedding in a transaction
    await prisma.$transaction(async (tx) => {
      console.log('🔄 Starting transaction...');
      
      // Generate FTS text
      const textToEmbed = await generateFtsTextForEntity(DESIGNIFY_ENTITY_ID, tx);

      if (!textToEmbed || textToEmbed.trim().length === 0) {
        throw new Error('No text content available for embedding generation');
      }

      // Generate embedding with retry
      const embedding = await generateEmbeddingWithRetry(textToEmbed);

      // Save to database (same format as EntitiesService)
      console.log('💾 Saving embedding to database...');
      const vectorString = `[${embedding.join(',')}]`;
      const result = await tx.$executeRaw`
        UPDATE "public"."entities" 
        SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() 
        WHERE id = ${DESIGNIFY_ENTITY_ID}::uuid
      `;
      
      if (result === 0) {
        throw new Error('Failed to update entity - entity not found');
      }

      console.log('✅ Embedding saved successfully!');
    });

    // Verify the fix
    console.log('');
    console.log('🔍 Verifying the fix...');
    
    const updatedEntity = await prisma.entity.findUnique({
      where: { id: DESIGNIFY_ENTITY_ID },
      select: {
        id: true,
        name: true,
        vectorEmbedding: true
      }
    });

    if (updatedEntity.vectorEmbedding) {
      console.log('✅ SUCCESS: Entity now has an embedding!');
    } else {
      console.log('❌ FAILED: Entity still missing embedding');
    }

    // Check overall status
    const totalCount = await prisma.entity.count();
    const withEmbeddings = await prisma.entity.count({
      where: { vectorEmbedding: { not: null } }
    });
    const missingEmbeddings = totalCount - withEmbeddings;

    console.log('');
    console.log('📊 Overall Database Status:');
    console.log(`   Total entities: ${totalCount}`);
    console.log(`   With embeddings: ${withEmbeddings}`);
    console.log(`   Missing embeddings: ${missingEmbeddings}`);

    if (missingEmbeddings === 0) {
      console.log('');
      console.log('🎉 MISSION ACCOMPLISHED!');
      console.log('All entities now have embeddings!');
    }

  } catch (error) {
    console.error('💥 Error during fix:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixDesignifyEmbedding().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
