#!/bin/bash

# Execute the embedding update directly using psql
# This script applies the embedding to the Designify entity using the database connection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Executing Embedding Update for Designify (Direct)${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EMBEDDING_FILE="$SCRIPT_DIR/designify-embedding.json"

# Check if embedding file exists
if [ ! -f "$EMBEDDING_FILE" ]; then
    echo -e "${RED}❌ Embedding file not found: $EMBEDDING_FILE${NC}"
    echo "Please run generate-designify-embedding.sh first"
    exit 1
fi

echo -e "${GRE<PERSON>}✅ Found embedding file${NC}"

# Extract DATABASE_URL from .env file
if [ -f .env ]; then
    DATABASE_URL=$(grep "^DATABASE_URL=" .env | cut -d'=' -f2)
    echo -e "${GREEN}✅ Found DATABASE_URL in .env${NC}"
else
    echo -e "${RED}❌ .env file not found${NC}"
    exit 1
fi

if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ DATABASE_URL not found in .env file${NC}"
    exit 1
fi

# Read the embedding (remove newlines and spaces)
EMBEDDING=$(cat "$EMBEDDING_FILE" | tr -d '\n' | tr -d ' ')
echo -e "${BLUE}📏 Embedding dimensions: $(echo "$EMBEDDING" | jq '. | length')${NC}"

# Create the SQL update statement
SQL_STATEMENT="UPDATE entities SET vector_embedding = '$EMBEDDING'::vector, updated_at = NOW() WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';"

echo -e "${BLUE}🔄 Executing UPDATE statement...${NC}"

# Execute the SQL using psql
if command -v psql >/dev/null 2>&1; then
    echo "$SQL_STATEMENT" | psql "$DATABASE_URL"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Update executed successfully!${NC}"
        
        # Verify the update
        echo -e "${BLUE}🔍 Verifying the update...${NC}"
        VERIFY_SQL="SELECT id, name, CASE WHEN vector_embedding IS NULL THEN 'MISSING' ELSE 'PRESENT' END as embedding_status, updated_at FROM entities WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';"
        
        echo "$VERIFY_SQL" | psql "$DATABASE_URL"
        
        # Check overall status
        echo -e "${BLUE}📊 Overall embedding status:${NC}"
        OVERALL_SQL="SELECT COUNT(*) as total_entities, COUNT(vector_embedding) as entities_with_embeddings, COUNT(*) - COUNT(vector_embedding) as missing_embeddings FROM entities;"
        
        echo "$OVERALL_SQL" | psql "$DATABASE_URL"
        
        echo ""
        echo -e "${GREEN}🎉 Embedding update completed successfully!${NC}"
        
    else
        echo -e "${RED}❌ Update failed${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ psql command not found${NC}"
    echo "Please install PostgreSQL client tools or use an alternative method"
    echo ""
    echo -e "${YELLOW}💡 Alternative: Copy and paste this SQL into Supabase SQL Editor:${NC}"
    echo ""
    echo "$SQL_STATEMENT"
    echo ""
    echo -e "${YELLOW}Then run this verification query:${NC}"
    echo "SELECT id, name, CASE WHEN vector_embedding IS NULL THEN 'MISSING' ELSE 'PRESENT' END as embedding_status FROM entities WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';"
    exit 1
fi
