#!/bin/bash

# Production Embedding Backfill Runner
# This script handles the execution of embedding backfill with proper environment setup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKFILL_SCRIPT="$SCRIPT_DIR/production-backfill-embeddings.js"

echo -e "${BLUE}🚀 Production Embedding Backfill Runner${NC}"
echo -e "${BLUE}=======================================${NC}"
echo ""

# Check if we're in the right directory
if [ ! -f "$PROJECT_ROOT/package.json" ]; then
    echo -e "${RED}❌ Error: Not in project root directory${NC}"
    echo "Please run this script from the project root or ensure package.json exists"
    exit 1
fi

# Check if the backfill script exists
if [ ! -f "$BACKFILL_SCRIPT" ]; then
    echo -e "${RED}❌ Error: Backfill script not found at $BACKFILL_SCRIPT${NC}"
    exit 1
fi

# Check for required environment variables
if [ -z "$OPENAI_API_KEY" ]; then
    echo -e "${YELLOW}⚠️  Warning: OPENAI_API_KEY not set in environment${NC}"
    echo "Checking .env file..."
    
    if [ -f "$PROJECT_ROOT/.env" ]; then
        echo -e "${GREEN}✅ Found .env file, sourcing it...${NC}"
        source "$PROJECT_ROOT/.env"
        
        if [ -z "$OPENAI_API_KEY" ]; then
            echo -e "${RED}❌ Error: OPENAI_API_KEY not found in .env file${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Error: No .env file found and OPENAI_API_KEY not set${NC}"
        exit 1
    fi
fi

if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ Error: DATABASE_URL not set${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables validated${NC}"

# Function to find Node.js
find_node() {
    # Try common locations and methods
    local node_paths=(
        "$(which node 2>/dev/null)"
        "$(which nodejs 2>/dev/null)"
        "/usr/bin/node"
        "/usr/local/bin/node"
        "/opt/homebrew/bin/node"
        "$HOME/.nvm/versions/node/*/bin/node"
        "./node_modules/.bin/node"
    )
    
    for path in "${node_paths[@]}"; do
        if [ -n "$path" ] && [ -x "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    # Try using npx if available
    if command -v npx >/dev/null 2>&1; then
        echo "npx node"
        return 0
    fi
    
    # Try using npm if available
    if command -v npm >/dev/null 2>&1; then
        echo "npm exec node"
        return 0
    fi
    
    return 1
}

# Find Node.js executable
echo -e "${BLUE}🔍 Looking for Node.js...${NC}"
NODE_CMD=$(find_node)

if [ $? -ne 0 ] || [ -z "$NODE_CMD" ]; then
    echo -e "${RED}❌ Error: Node.js not found${NC}"
    echo "Please install Node.js or ensure it's in your PATH"
    echo "You can install Node.js from: https://nodejs.org/"
    exit 1
fi

echo -e "${GREEN}✅ Found Node.js: $NODE_CMD${NC}"

# Check Node.js version
echo -e "${BLUE}📋 Checking Node.js version...${NC}"
NODE_VERSION=$($NODE_CMD --version 2>/dev/null || echo "unknown")
echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"

# Parse command line arguments
DRY_RUN=false
BATCH_SIZE=50
FORCE=false
RATE_LIMIT=1100

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --batch-size=*)
            BATCH_SIZE="${1#*=}"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --rate-limit=*)
            RATE_LIMIT="${1#*=}"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --dry-run          Show what would be processed without making changes"
            echo "  --batch-size=N     Number of entities per batch (default: 50)"
            echo "  --force            Skip confirmation prompts"
            echo "  --rate-limit=N     Delay between requests in ms (default: 1100)"
            echo "  --help, -h         Show this help message"
            echo ""
            echo "Environment variables required:"
            echo "  OPENAI_API_KEY     OpenAI API key for generating embeddings"
            echo "  DATABASE_URL       PostgreSQL connection string"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ Unknown option: $1${NC}"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Build command arguments
CMD_ARGS=""
if [ "$DRY_RUN" = true ]; then
    CMD_ARGS="$CMD_ARGS --dry-run"
fi
if [ "$FORCE" = true ]; then
    CMD_ARGS="$CMD_ARGS --force"
fi
CMD_ARGS="$CMD_ARGS --batch-size=$BATCH_SIZE --rate-limit=$RATE_LIMIT"

echo -e "${BLUE}📊 Configuration:${NC}"
echo "  Dry Run: $DRY_RUN"
echo "  Batch Size: $BATCH_SIZE"
echo "  Rate Limit: ${RATE_LIMIT}ms"
echo "  Force: $FORCE"
echo ""

# Change to project directory
cd "$PROJECT_ROOT"

# Run the backfill script
echo -e "${BLUE}🚀 Starting embedding backfill...${NC}"
echo -e "${YELLOW}Command: $NODE_CMD $BACKFILL_SCRIPT $CMD_ARGS${NC}"
echo ""

# Execute the script
if [ "$NODE_CMD" = "npx node" ]; then
    npx node "$BACKFILL_SCRIPT" $CMD_ARGS
elif [ "$NODE_CMD" = "npm exec node" ]; then
    npm exec node "$BACKFILL_SCRIPT" $CMD_ARGS
else
    "$NODE_CMD" "$BACKFILL_SCRIPT" $CMD_ARGS
fi

SCRIPT_EXIT_CODE=$?

echo ""
if [ $SCRIPT_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}🎉 Embedding backfill completed successfully!${NC}"
else
    echo -e "${RED}❌ Embedding backfill failed with exit code: $SCRIPT_EXIT_CODE${NC}"
    exit $SCRIPT_EXIT_CODE
fi
