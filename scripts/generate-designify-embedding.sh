#!/bin/bash

# Generate Embedding for Designify Entity
# This script calls OpenAI API directly to generate the embedding

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Generating Embedding for Designify Entity${NC}"
echo -e "${BLUE}===========================================${NC}"
echo ""

# Extract OpenAI API key from .env file
if [ -f .env ]; then
    OPENAI_API_KEY=$(grep "^OPENAI_API_KEY=" .env | cut -d'=' -f2)
    echo -e "${GREEN}✅ Found .env file${NC}"
else
    echo -e "${RED}❌ .env file not found${NC}"
    exit 1
fi

# Check for OpenAI API key
if [ -z "$OPENAI_API_KEY" ]; then
    echo -e "${RED}❌ OPENAI_API_KEY not found in .env file${NC}"
    exit 1
fi

echo -e "${GREEN}✅ OpenAI API key found${NC}"

# Read the text to embed
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEXT_FILE="$SCRIPT_DIR/designify-embedding-text.txt"

if [ ! -f "$TEXT_FILE" ]; then
    echo -e "${RED}❌ Text file not found: $TEXT_FILE${NC}"
    exit 1
fi

TEXT_TO_EMBED=$(cat "$TEXT_FILE")
echo -e "${BLUE}📝 Text to embed (${#TEXT_TO_EMBED} characters):${NC}"
echo "\"${TEXT_TO_EMBED:0:200}...\""
echo ""

# Create JSON payload
JSON_PAYLOAD=$(cat <<EOF
{
  "input": "$TEXT_TO_EMBED",
  "model": "text-embedding-3-small",
  "encoding_format": "float"
}
EOF
)

echo -e "${BLUE}🔄 Calling OpenAI API...${NC}"

# Call OpenAI API
RESPONSE=$(curl -s -X POST \
  https://api.openai.com/v1/embeddings \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -H "Content-Type: application/json" \
  -d "$JSON_PAYLOAD")

# Check if the request was successful
if echo "$RESPONSE" | grep -q '"error"'; then
    echo -e "${RED}❌ OpenAI API Error:${NC}"
    echo "$RESPONSE" | jq '.error'
    exit 1
fi

# Extract the embedding
EMBEDDING=$(echo "$RESPONSE" | jq -r '.data[0].embedding')
USAGE=$(echo "$RESPONSE" | jq -r '.usage')

if [ "$EMBEDDING" = "null" ] || [ -z "$EMBEDDING" ]; then
    echo -e "${RED}❌ Failed to extract embedding from response${NC}"
    echo "Response: $RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Embedding generated successfully!${NC}"
echo -e "${BLUE}📊 Usage: $USAGE${NC}"

# Count dimensions
DIMENSION_COUNT=$(echo "$EMBEDDING" | jq '. | length')
echo -e "${BLUE}📏 Dimensions: $DIMENSION_COUNT${NC}"

# Save embedding to file
EMBEDDING_FILE="$SCRIPT_DIR/designify-embedding.json"
echo "$EMBEDDING" > "$EMBEDDING_FILE"
echo -e "${GREEN}💾 Embedding saved to: $EMBEDDING_FILE${NC}"

# Create SQL update statement
SQL_FILE="$SCRIPT_DIR/update-designify-embedding.sql"
cat > "$SQL_FILE" <<EOF
-- Update Designify entity with generated embedding
UPDATE entities 
SET vector_embedding = '$EMBEDDING'::vector,
    updated_at = NOW()
WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';

-- Verify the update
SELECT 
    id,
    name,
    CASE 
        WHEN vector_embedding IS NULL THEN 'MISSING'
        ELSE 'PRESENT'
    END as embedding_status,
    updated_at
FROM entities 
WHERE id = '083b237b-e6ff-420e-a2cf-bfd87661372c';

-- Check overall status
SELECT 
    COUNT(*) as total_entities,
    COUNT(vector_embedding) as entities_with_embeddings,
    COUNT(*) - COUNT(vector_embedding) as missing_embeddings
FROM entities;
EOF

echo -e "${GREEN}📄 SQL update file created: $SQL_FILE${NC}"
echo ""
echo -e "${YELLOW}🔧 Next steps:${NC}"
echo "1. Review the generated SQL file: $SQL_FILE"
echo "2. Execute the SQL to update the database"
echo "3. Verify that all entities now have embeddings"
echo ""
echo -e "${GREEN}🎉 Embedding generation completed successfully!${NC}"
