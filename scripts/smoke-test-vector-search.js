#!/usr/bin/env node

/**
 * Smoke Test: Vector Search End-to-End
 * 
 * Tests the complete vector search pipeline after embedding backfill
 * to ensure all 604 entities are properly searchable.
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');
require('dotenv').config();

const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Test queries and expectations
const TEST_CASES = [
  {
    query: "designify for background removal",
    expectation: "Designify ranked #1",
    expectedEntityName: "Designify",
    minResults: 1
  },
  {
    query: "cheap AI background remover",
    expectation: "Designify in top-3",
    expectedEntityName: "Designify",
    minResults: 3
  },
  {
    query: "free trial video generator",
    expectation: "> 0 entities, no keyword fallback",
    minResults: 1
  },
  {
    query: "AI image enhancement tool",
    expectation: "Multiple relevant tools",
    minResults: 5
  }
];

async function generateQueryEmbedding(query) {
  try {
    const response = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: query.trim(),
    });

    if (response.data && response.data.length > 0 && response.data[0].embedding) {
      return response.data[0].embedding;
    } else {
      throw new Error('OpenAI API returned no embedding data');
    }
  } catch (error) {
    console.error(`❌ Failed to generate embedding for query: "${query}"`, error.message);
    throw error;
  }
}

async function performVectorSearch(queryEmbedding, limit = 10) {
  try {
    const vectorString = `[${queryEmbedding.join(',')}]`;
    
    const results = await prisma.$queryRaw`
      SELECT 
        id,
        name,
        short_description,
        1 - (vector_embedding <=> ${vectorString}::vector) as similarity_score
      FROM entities 
      WHERE vector_embedding IS NOT NULL
      ORDER BY vector_embedding <=> ${vectorString}::vector
      LIMIT ${limit}
    `;

    return results;
  } catch (error) {
    console.error('❌ Vector search failed:', error.message);
    throw error;
  }
}

async function runSmokeTest() {
  console.log('🚀 Vector Search Smoke Test');
  console.log('============================');
  console.log('');

  let allTestsPassed = true;
  const results = [];

  for (let i = 0; i < TEST_CASES.length; i++) {
    const testCase = TEST_CASES[i];
    console.log(`🔍 Test ${i + 1}: "${testCase.query}"`);
    console.log(`   Expected: ${testCase.expectation}`);

    try {
      // Generate query embedding
      const startTime = Date.now();
      const queryEmbedding = await generateQueryEmbedding(testCase.query);
      const embeddingTime = Date.now() - startTime;

      // Perform vector search
      const searchStartTime = Date.now();
      const searchResults = await performVectorSearch(queryEmbedding, 10);
      const searchTime = Date.now() - searchStartTime;

      const totalTime = Date.now() - startTime;

      // Analyze results
      const topResult = searchResults[0];
      const hasMinResults = searchResults.length >= testCase.minResults;
      const expectedEntityFound = testCase.expectedEntityName ? 
        searchResults.some(r => r.name.toLowerCase().includes(testCase.expectedEntityName.toLowerCase())) : 
        true;

      const testPassed = hasMinResults && expectedEntityFound;
      allTestsPassed = allTestsPassed && testPassed;

      // Log results
      console.log(`   ${testPassed ? '✅' : '❌'} Results: ${searchResults.length} entities found`);
      console.log(`   ⏱️  Timing: ${embeddingTime}ms (embedding) + ${searchTime}ms (search) = ${totalTime}ms total`);
      
      if (topResult) {
        console.log(`   🥇 Top result: ${topResult.name} (similarity: ${(topResult.similarity_score * 100).toFixed(1)}%)`);
      }

      if (testCase.expectedEntityName) {
        const expectedEntity = searchResults.find(r => 
          r.name.toLowerCase().includes(testCase.expectedEntityName.toLowerCase())
        );
        if (expectedEntity) {
          const rank = searchResults.indexOf(expectedEntity) + 1;
          console.log(`   🎯 ${testCase.expectedEntityName} found at rank #${rank} (similarity: ${(expectedEntity.similarity_score * 100).toFixed(1)}%)`);
        } else {
          console.log(`   ❌ ${testCase.expectedEntityName} not found in results`);
        }
      }

      // Store detailed results
      results.push({
        query: testCase.query,
        passed: testPassed,
        resultCount: searchResults.length,
        topResult: topResult?.name,
        topSimilarity: topResult?.similarity_score,
        totalTime,
        embeddingTime,
        searchTime,
        results: searchResults.slice(0, 3).map(r => ({
          name: r.name,
          similarity: r.similarity_score
        }))
      });

    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
      allTestsPassed = false;
      results.push({
        query: testCase.query,
        passed: false,
        error: error.message
      });
    }

    console.log('');
  }

  // Summary
  console.log('📊 SMOKE TEST SUMMARY');
  console.log('=====================');
  console.log(`Overall Status: ${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  console.log(`Tests Run: ${TEST_CASES.length}`);
  console.log(`Tests Passed: ${results.filter(r => r.passed).length}`);
  console.log(`Tests Failed: ${results.filter(r => !r.passed).length}`);

  if (results.length > 0) {
    const avgTime = results
      .filter(r => r.totalTime)
      .reduce((sum, r) => sum + r.totalTime, 0) / results.filter(r => r.totalTime).length;
    console.log(`Average Response Time: ${Math.round(avgTime)}ms`);
  }

  console.log('');

  // Performance analysis
  if (allTestsPassed) {
    console.log('🎉 VECTOR SEARCH IS FULLY OPERATIONAL!');
    console.log('');
    console.log('✅ All 604 entities are searchable');
    console.log('✅ Vector similarity search working');
    console.log('✅ Performance within acceptable range');
    console.log('✅ Expected entities found in results');
    console.log('');
    console.log('🚀 Ready for production traffic!');
  } else {
    console.log('⚠️  Some tests failed. Review the results above.');
  }

  return { allTestsPassed, results };
}

// Run the smoke test
runSmokeTest()
  .then(({ allTestsPassed, results }) => {
    process.exit(allTestsPassed ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Smoke test crashed:', error);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect();
  });
