#!/usr/bin/env node

/**
 * Vector Search Health Monitor
 * 
 * Monitors the health of the vector search system and alerts
 * if performance degrades or embeddings go missing.
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function checkEmbeddingCoverage() {
  const stats = await prisma.$queryRaw`
    SELECT 
      COUNT(*) as total_entities,
      COUNT(vector_embedding) as entities_with_embeddings,
      COUNT(*) - COUNT(vector_embedding) as missing_embeddings
    FROM entities
  `;
  
  return stats[0];
}

async function checkVectorIndexHealth() {
  try {
    const indexStats = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        indexname,
        pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
      FROM pg_indexes 
      WHERE indexname = 'entities_vector_embedding_idx'
    `;
    
    return indexStats[0] || null;
  } catch (error) {
    return { error: error.message };
  }
}

async function testVectorSearchPerformance() {
  const testQueries = [
    "AI image editing tool",
    "background removal software", 
    "video generation platform"
  ];
  
  const results = [];
  
  for (const query of testQueries) {
    try {
      const startTime = Date.now();
      
      // Simple vector search test (using a known entity's embedding)
      const searchResults = await prisma.$queryRaw`
        SELECT name, short_description
        FROM entities 
        WHERE vector_embedding IS NOT NULL
        ORDER BY vector_embedding <=> (
          SELECT vector_embedding 
          FROM entities 
          WHERE name = 'Designify' 
          LIMIT 1
        )
        LIMIT 5
      `;
      
      const duration = Date.now() - startTime;
      
      results.push({
        query,
        duration,
        resultCount: searchResults.length,
        success: true
      });
      
    } catch (error) {
      results.push({
        query,
        success: false,
        error: error.message
      });
    }
  }
  
  return results;
}

async function runHealthCheck() {
  console.log('🏥 Vector Search Health Check');
  console.log('=============================');
  console.log(`Timestamp: ${new Date().toISOString()}`);
  console.log('');

  // Check embedding coverage
  console.log('📊 Embedding Coverage:');
  const coverage = await checkEmbeddingCoverage();
  console.log(`   Total entities: ${coverage.total_entities}`);
  console.log(`   With embeddings: ${coverage.entities_with_embeddings}`);
  console.log(`   Missing embeddings: ${coverage.missing_embeddings}`);
  
  const coverageHealth = coverage.missing_embeddings === 0 ? '✅ HEALTHY' : '❌ DEGRADED';
  console.log(`   Status: ${coverageHealth}`);
  console.log('');

  // Check vector index
  console.log('🔍 Vector Index Health:');
  const indexHealth = await checkVectorIndexHealth();
  if (indexHealth.error) {
    console.log(`   ❌ Index check failed: ${indexHealth.error}`);
  } else if (indexHealth) {
    console.log(`   ✅ Index exists: ${indexHealth.indexname}`);
    console.log(`   📏 Index size: ${indexHealth.index_size}`);
  } else {
    console.log(`   ❌ Vector index not found`);
  }
  console.log('');

  // Test performance
  console.log('⚡ Performance Test:');
  const perfResults = await testVectorSearchPerformance();
  const avgDuration = perfResults
    .filter(r => r.success)
    .reduce((sum, r) => sum + r.duration, 0) / perfResults.filter(r => r.success).length;
  
  const successCount = perfResults.filter(r => r.success).length;
  console.log(`   Successful queries: ${successCount}/${perfResults.length}`);
  console.log(`   Average response time: ${Math.round(avgDuration)}ms`);
  
  const perfHealth = avgDuration < 2000 && successCount === perfResults.length ? '✅ HEALTHY' : '⚠️ SLOW';
  console.log(`   Performance status: ${perfHealth}`);
  console.log('');

  // Overall health
  const overallHealth = 
    coverage.missing_embeddings === 0 && 
    indexHealth && !indexHealth.error && 
    avgDuration < 2000 && 
    successCount === perfResults.length;

  console.log('🎯 OVERALL HEALTH:');
  console.log(`   Status: ${overallHealth ? '✅ SYSTEM HEALTHY' : '⚠️ ISSUES DETECTED'}`);
  
  if (!overallHealth) {
    console.log('');
    console.log('🚨 RECOMMENDED ACTIONS:');
    if (coverage.missing_embeddings > 0) {
      console.log(`   - Run embedding backfill for ${coverage.missing_embeddings} entities`);
    }
    if (!indexHealth || indexHealth.error) {
      console.log(`   - Recreate vector index`);
    }
    if (avgDuration >= 2000) {
      console.log(`   - Investigate query performance (${Math.round(avgDuration)}ms avg)`);
    }
  }

  return {
    healthy: overallHealth,
    coverage,
    indexHealth,
    performance: {
      avgDuration,
      successRate: successCount / perfResults.length
    }
  };
}

// Run if called directly
if (require.main === module) {
  runHealthCheck()
    .then(result => {
      process.exit(result.healthy ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Health check failed:', error);
      process.exit(1);
    })
    .finally(() => {
      prisma.$disconnect();
    });
}

module.exports = { runHealthCheck };
