/**
 * Manual script to backfill embeddings for entities that don't have them
 * This can be run independently of the API to fix existing entities
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');

// Initialize clients
const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

async function generateFtsTextForEntity(entityId, tx) {
  const entityWithDetails = await tx.entity.findUniqueOrThrow({
    where: { id: entityId },
    include: {
      entityType: true,
      entityCategories: { include: { category: true } },
      entityTags: { include: { tag: true } },
      entityFeatures: { include: { feature: true } },
      entityDetailsTool: true,
      entityDetailsCourse: true,
      entityDetailsAgency: true,
      entityDetailsContentCreator: true,
      entityDetailsCommunity: true,
      entityDetailsNewsletter: true,
      entityDetailsDataset: true,
      entityDetailsResearchPaper: true,
      entityDetailsSoftware: true,
      entityDetailsModel: true,
      entityDetailsProjectReference: true,
      entityDetailsServiceProvider: true,
      entityDetailsInvestor: true,
      entityDetailsEvent: true,
      entityDetailsJob: true,
      entityDetailsGrant: true,
      entityDetailsBounty: true,
      entityDetailsHardware: true,
      entityDetailsNews: true,
      entityDetailsBook: true,
      entityDetailsPodcast: true,
      entityDetailsPlatform: true,
    },
  });

  let textToEmbed = `${entityWithDetails.name || ''}. ${
    entityWithDetails.shortDescription || ''
  }. ${entityWithDetails.description || ''}.`;

  if (entityWithDetails.entityCategories.length > 0) {
    textToEmbed += ` Categories: ${entityWithDetails.entityCategories
      .map(ec => ec.category.name)
      .join(', ')}.`;
  }
  if (entityWithDetails.entityTags.length > 0) {
    textToEmbed += ` Tags: ${entityWithDetails.entityTags
      .map(et => et.tag.name)
      .join(', ')}.`;
  }

  // Add entity type specific details
  const details = 
    entityWithDetails.entityDetailsTool ||
    entityWithDetails.entityDetailsCourse ||
    entityWithDetails.entityDetailsAgency ||
    entityWithDetails.entityDetailsContentCreator ||
    entityWithDetails.entityDetailsCommunity ||
    entityWithDetails.entityDetailsNewsletter ||
    entityWithDetails.entityDetailsDataset ||
    entityWithDetails.entityDetailsResearchPaper ||
    entityWithDetails.entityDetailsSoftware ||
    entityWithDetails.entityDetailsModel ||
    entityWithDetails.entityDetailsProjectReference ||
    entityWithDetails.entityDetailsServiceProvider ||
    entityWithDetails.entityDetailsInvestor ||
    entityWithDetails.entityDetailsEvent ||
    entityWithDetails.entityDetailsJob ||
    entityWithDetails.entityDetailsGrant ||
    entityWithDetails.entityDetailsBounty ||
    entityWithDetails.entityDetailsHardware ||
    entityWithDetails.entityDetailsNews ||
    entityWithDetails.entityDetailsBook ||
    entityWithDetails.entityDetailsPodcast ||
    entityWithDetails.entityDetailsPlatform;

  if (details) {
    if ('keyFeatures' in details && Array.isArray(details.keyFeatures)) {
      textToEmbed += ` Key Features: ${details.keyFeatures.join(', ')}.`;
    }
    if ('useCases' in details && Array.isArray(details.useCases)) {
      textToEmbed += ` Use Cases: ${details.useCases.join(', ')}.`;
    }
  }
  return textToEmbed;
}

async function generateEmbeddingWithRetry(text, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`  Attempt ${attempt}/${maxRetries}: Calling OpenAI service...`);
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text.trim(),
      });
      
      if (response.data && response.data.length > 0 && response.data[0].embedding) {
        console.log(`  Attempt ${attempt} successful: Embedding length ${response.data[0].embedding.length}`);
        return response.data[0].embedding;
      } else {
        throw new Error('OpenAI returned invalid embedding response');
      }
    } catch (error) {
      lastError = error;
      console.warn(`  Attempt ${attempt}/${maxRetries} failed: ${error.message}`);
      
      // Don't retry on certain types of errors
      if (error.message?.includes('API key') || 
          error.message?.includes('quota') || 
          error.message?.includes('billing') ||
          error.message?.includes('unauthorized')) {
        console.error(`  Non-retryable error detected: ${error.message}`);
        throw error;
      }
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        console.log(`  Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
  
  throw new Error(`Embedding generation failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
}

async function backfillEmbeddings(batchSize = 10) {
  console.log('🚀 Starting embedding backfill process...\n');
  
  try {
    // Check current status
    const totalEntities = await prisma.entity.count();
    const entitiesWithoutEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: null,
        status: 'ACTIVE'
      }
    });

    console.log(`📊 Status Overview:`);
    console.log(`   Total entities: ${totalEntities}`);
    console.log(`   Active entities without embeddings: ${entitiesWithoutEmbeddings}`);

    if (entitiesWithoutEmbeddings === 0) {
      console.log('✅ All active entities already have embeddings!');
      return;
    }

    // Get entities to process
    const entitiesToProcess = await prisma.entity.findMany({
      where: {
        vectorEmbedding: null,
        status: 'ACTIVE'
      },
      select: {
        id: true,
        name: true,
      },
      take: batchSize,
      orderBy: {
        createdAt: 'asc'
      }
    });

    console.log(`\n🔄 Processing ${entitiesToProcess.length} entities...\n`);

    let processed = 0;
    const failed = [];

    for (const entity of entitiesToProcess) {
      try {
        console.log(`Processing: ${entity.name} (${entity.id})`);
        
        await prisma.$transaction(async (tx) => {
          // Generate FTS text
          const textToEmbed = await generateFtsTextForEntity(entity.id, tx);
          console.log(`  Text prepared: "${textToEmbed.substring(0, 100)}..."`);

          if (!textToEmbed || textToEmbed.trim().length === 0) {
            throw new Error('No text content available for embedding generation');
          }

          // Generate embedding with retry
          const embedding = await generateEmbeddingWithRetry(textToEmbed);

          // Save to database
          const vectorString = `[${embedding.join(',')}]`;
          const result = await tx.$executeRaw`UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entity.id}::uuid`;
          
          if (result === 0) {
            throw new Error('Failed to update entity - entity not found');
          }

          console.log(`  ✅ Successfully processed: ${entity.name}`);
        });
        
        processed++;
        
        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`  ❌ Failed to process ${entity.name}: ${error.message}`);
        failed.push(`${entity.name} (${entity.id}): ${error.message}`);
      }
    }

    console.log(`\n🎉 Backfill completed!`);
    console.log(`   Processed: ${processed}`);
    console.log(`   Failed: ${failed.length}`);
    
    if (failed.length > 0) {
      console.log(`\n❌ Failed entities:`);
      failed.forEach(failure => console.log(`   - ${failure}`));
    }

    // Final status check
    const remainingWithoutEmbeddings = await prisma.entity.count({
      where: {
        vectorEmbedding: null,
        status: 'ACTIVE'
      }
    });

    console.log(`\n📈 Final Status:`);
    console.log(`   Remaining active entities without embeddings: ${remainingWithoutEmbeddings}`);

  } catch (error) {
    console.error('❌ Backfill process failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Parse command line arguments
const batchSize = process.argv[2] ? parseInt(process.argv[2]) : 10;

if (isNaN(batchSize) || batchSize < 1) {
  console.error('❌ Invalid batch size. Please provide a positive number.');
  console.log('Usage: node scripts/backfill-embeddings.js [batchSize]');
  console.log('Example: node scripts/backfill-embeddings.js 20');
  process.exit(1);
}

// Run the backfill
backfillEmbeddings(batchSize);
