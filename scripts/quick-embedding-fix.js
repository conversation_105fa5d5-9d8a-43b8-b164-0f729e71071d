/**
 * Quick Embedding Fix for Single Entity
 * 
 * This script handles the immediate issue of the one missing embedding
 * for the "Designify" entity. It's a simplified version that can be run
 * directly to fix the current gap.
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');
require('dotenv').config();

const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function generateEmbedding(text) {
  try {
    console.log('🔄 Generating embedding...');
    const response = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: text.slice(0, 8192), // Ensure we don't exceed token limits
    });

    if (response.data && response.data.length > 0 && response.data[0].embedding) {
      console.log(`✅ Embedding generated (${response.data[0].embedding.length} dimensions)`);
      return response.data[0].embedding;
    } else {
      throw new Error('OpenAI API returned no embedding data');
    }
  } catch (error) {
    console.error('❌ Failed to generate embedding:', error.message);
    throw error;
  }
}

async function fixMissingEmbedding() {
  console.log('🚀 Quick Embedding Fix Starting...');
  console.log('==================================');
  
  try {
    // Find entities without embeddings
    const entitiesWithoutEmbeddings = await prisma.entity.findMany({
      where: {
        vectorEmbedding: null
      },
      select: {
        id: true,
        name: true,
        shortDescription: true,
        description: true
      }
    });

    console.log(`📊 Found ${entitiesWithoutEmbeddings.length} entities without embeddings`);

    if (entitiesWithoutEmbeddings.length === 0) {
      console.log('🎉 All entities already have embeddings!');
      return;
    }

    // Process each entity
    for (const entity of entitiesWithoutEmbeddings) {
      console.log(`\n🔄 Processing: ${entity.name} (${entity.id})`);
      
      try {
        // Build text for embedding
        const textParts = [];
        textParts.push(entity.name);
        if (entity.shortDescription) textParts.push(entity.shortDescription);
        if (entity.description) textParts.push(entity.description);
        
        const textToEmbed = textParts.join(' ').trim();
        console.log(`📝 Text prepared (${textToEmbed.length} chars): "${textToEmbed.substring(0, 100)}..."`);

        if (!textToEmbed || textToEmbed.length === 0) {
          console.log('⚠️  Skipping entity with no text content');
          continue;
        }

        // Generate embedding
        const embedding = await generateEmbedding(textToEmbed);

        // Save to database
        const vectorString = `[${embedding.join(',')}]`;
        const result = await prisma.$executeRaw`
          UPDATE "public"."entities" 
          SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() 
          WHERE id = ${entity.id}::uuid
        `;
        
        if (result === 0) {
          throw new Error('Failed to update entity - entity not found');
        }

        console.log(`✅ Successfully saved embedding for ${entity.name}`);
        
        // Small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1100));

      } catch (error) {
        console.error(`❌ Failed to process ${entity.name}:`, error.message);
      }
    }

    // Verify results
    const remainingCount = await prisma.entity.count({
      where: { vectorEmbedding: null }
    });
    
    console.log('\n🎯 SUMMARY');
    console.log('==========');
    if (remainingCount === 0) {
      console.log('✅ All entities now have embeddings!');
    } else {
      console.log(`⚠️  ${remainingCount} entities still missing embeddings`);
    }

  } catch (error) {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
fixMissingEmbedding().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
