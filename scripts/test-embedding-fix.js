/**
 * Test script to verify that the embedding generation fix is working
 * This script will create a test entity and verify that embeddings are generated automatically
 */

const { PrismaClient } = require('@prisma/client');
const OpenAI = require('openai');

// Initialize clients
const prisma = new PrismaClient();
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

async function testEmbeddingFix() {
  console.log('🧪 Testing embedding generation fix...\n');
  
  try {
    // Test 1: Verify OpenAI API is working
    console.log('📝 Test 1: Verifying OpenAI API connection...');
    try {
      const testEmbedding = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: 'Test embedding generation',
      });
      
      if (testEmbedding.data && testEmbedding.data[0].embedding) {
        console.log('✅ OpenAI API connection successful');
        console.log(`   Embedding length: ${testEmbedding.data[0].embedding.length}`);
      } else {
        throw new Error('Invalid response from OpenAI API');
      }
    } catch (error) {
      console.error('❌ OpenAI API test failed:', error.message);
      return;
    }

    // Test 2: Check current embedding status
    console.log('\n📝 Test 2: Checking current embedding status...');
    const totalEntities = await prisma.entity.count();
    const entitiesWithEmbeddings = await prisma.entity.count({
      where: { vectorEmbedding: { not: null } }
    });
    const entitiesWithoutEmbeddings = await prisma.entity.count({
      where: { vectorEmbedding: null }
    });

    console.log(`   Total entities: ${totalEntities}`);
    console.log(`   With embeddings: ${entitiesWithEmbeddings}`);
    console.log(`   Without embeddings: ${entitiesWithoutEmbeddings}`);

    // Test 3: Get required data for entity creation
    console.log('\n📝 Test 3: Preparing test entity data...');
    
    // Get an entity type (preferably AI tool)
    const entityType = await prisma.entityType.findFirst({
      where: { slug: 'ai-tool' }
    });
    
    if (!entityType) {
      console.error('❌ No AI tool entity type found. Cannot create test entity.');
      return;
    }

    // Get a test user (first user in the database)
    const testUser = await prisma.user.findFirst();
    
    if (!testUser) {
      console.error('❌ No users found in database. Cannot create test entity.');
      return;
    }

    console.log(`   Entity type: ${entityType.name} (${entityType.id})`);
    console.log(`   Test user: ${testUser.email} (${testUser.id})`);

    // Test 4: Create a test entity and verify embedding generation
    console.log('\n📝 Test 4: Creating test entity with embedding...');
    
    const testEntityName = `Test Entity ${Date.now()}`;
    const testEntityDescription = 'This is a test entity created to verify that embeddings are automatically generated during entity creation.';
    
    console.log(`   Creating entity: ${testEntityName}`);
    
    let testEntityId;
    try {
      // Simulate the entity creation process
      const newEntity = await prisma.$transaction(async (tx) => {
        // Create the entity
        const entity = await tx.entity.create({
          data: {
            name: testEntityName,
            shortDescription: testEntityDescription,
            description: `${testEntityDescription} This entity includes additional details for comprehensive testing.`,
            logoUrl: 'https://example.com/test-logo.png',
            websiteUrl: 'https://example.com',
            entityTypeId: entityType.id,
            submitterId: testUser.id,
            status: 'PENDING',
            slug: `test-entity-${Date.now()}`,
          }
        });

        console.log(`   Entity created with ID: ${entity.id}`);
        testEntityId = entity.id;

        // Now test the embedding generation (this should work with our fix)
        console.log('   Generating embedding...');
        
        // Generate FTS text
        const entityWithDetails = await tx.entity.findUniqueOrThrow({
          where: { id: entity.id },
          include: {
            entityType: true,
            entityCategories: { include: { category: true } },
            entityTags: { include: { tag: true } },
            entityFeatures: { include: { feature: true } },
          },
        });

        let textToEmbed = `${entityWithDetails.name || ''}. ${
          entityWithDetails.shortDescription || ''
        }. ${entityWithDetails.description || ''}.`;

        console.log(`   Text to embed: "${textToEmbed.substring(0, 100)}..."`);

        // Generate embedding with retry logic
        let embedding;
        let lastError;
        const maxRetries = 3;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            console.log(`   Embedding attempt ${attempt}/${maxRetries}...`);
            const response = await openai.embeddings.create({
              model: 'text-embedding-3-small',
              input: textToEmbed.trim(),
            });
            
            if (response.data && response.data.length > 0 && response.data[0].embedding) {
              embedding = response.data[0].embedding;
              console.log(`   Embedding generated successfully (length: ${embedding.length})`);
              break;
            } else {
              throw new Error('OpenAI returned invalid embedding response');
            }
          } catch (error) {
            lastError = error;
            console.warn(`   Attempt ${attempt} failed: ${error.message}`);
            
            if (attempt < maxRetries) {
              const waitTime = Math.pow(2, attempt) * 1000;
              console.log(`   Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            }
          }
        }

        if (!embedding) {
          throw new Error(`Embedding generation failed after ${maxRetries} attempts. Last error: ${lastError.message}`);
        }

        // Save embedding to database
        console.log('   Saving embedding to database...');
        const vectorString = `[${embedding.join(',')}]`;
        const result = await tx.$executeRaw`UPDATE "public"."entities" SET "vector_embedding" = ${vectorString}::vector, "updated_at" = NOW() WHERE id = ${entity.id}::uuid`;
        
        if (result === 0) {
          throw new Error('Failed to update entity with embedding');
        }

        console.log('   Embedding saved successfully!');
        return entity;
      });

      console.log('✅ Test entity created successfully with embedding!');

    } catch (error) {
      console.error('❌ Entity creation failed:', error.message);
      console.error('   This indicates the embedding generation fix may not be working correctly.');
      return;
    }

    // Test 5: Verify the embedding was saved correctly
    console.log('\n📝 Test 5: Verifying embedding was saved...');
    
    const entityWithEmbedding = await prisma.entity.findUnique({
      where: { id: testEntityId },
      select: {
        id: true,
        name: true,
        vectorEmbedding: true,
      }
    });

    if (entityWithEmbedding && entityWithEmbedding.vectorEmbedding !== null) {
      console.log('✅ Embedding verification successful!');
      console.log(`   Entity: ${entityWithEmbedding.name}`);
      console.log(`   Has embedding: Yes`);
    } else {
      console.error('❌ Embedding verification failed - no embedding found in database');
      return;
    }

    // Test 6: Test vector search with the new entity
    console.log('\n📝 Test 6: Testing vector search with new entity...');
    
    try {
      const searchQuery = 'test entity verification';
      const searchEmbedding = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: searchQuery,
      });

      if (searchEmbedding.data && searchEmbedding.data[0].embedding) {
        const vectorString = `[${searchEmbedding.data[0].embedding.join(',')}]`;
        
        const searchResults = await prisma.$queryRaw`
          SELECT
            id,
            name,
            "short_description" as "shortDescription",
            1 - ( "vector_embedding" <=> ${vectorString}::vector ) as similarity
          FROM
            "public"."entities"
          WHERE
            1 - ( "vector_embedding" <=> ${vectorString}::vector ) > 0.1
            AND id = ${testEntityId}::uuid
          ORDER BY
            similarity DESC
          LIMIT 1;
        `;

        if (searchResults.length > 0) {
          console.log('✅ Vector search test successful!');
          console.log(`   Found entity: ${searchResults[0].name}`);
          console.log(`   Similarity: ${searchResults[0].similarity.toFixed(4)}`);
        } else {
          console.log('⚠️  Vector search test: No results found (this might be normal for a test entity)');
        }
      }
    } catch (error) {
      console.error('❌ Vector search test failed:', error.message);
    }

    // Cleanup: Remove test entity
    console.log('\n🧹 Cleaning up test entity...');
    try {
      await prisma.entity.delete({
        where: { id: testEntityId }
      });
      console.log('✅ Test entity cleaned up successfully');
    } catch (error) {
      console.warn(`⚠️  Failed to clean up test entity: ${error.message}`);
      console.log(`   You may need to manually delete entity with ID: ${testEntityId}`);
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('✅ The embedding generation fix is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testEmbeddingFix();
