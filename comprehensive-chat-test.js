#!/usr/bin/env node

/**
 * Comprehensive Chat System Test
 * Tests all implemented fixes with detailed analysis
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.x1nkcwYeYGVOplt1cAXsiPLvsGz8UNge1OSObjakRmo';

const TEST_QUERIES = [
  {
    name: 'Background Removal',
    message: 'I need a tool for removing backgrounds from images',
    expectedKeywords: ['remove.bg', 'designify', 'background', 'image'],
    testType: 'vector_search'
  },
  {
    name: 'Content Creation',
    message: 'AI tool for creating social media content',
    expectedKeywords: ['content', 'social', 'media', 'creation'],
    testType: 'keyword_search'
  },
  {
    name: 'Video Generation',
    message: 'best AI video generation',
    expectedKeywords: ['video', 'generation', 'runway', 'pika'],
    testType: 'hybrid_search'
  }
];

async function sendChatMessage(message, sessionId = null) {
  try {
    const payload = { message };
    if (sessionId) payload.session_id = sessionId;

    const response = await axios.post(`${BASE_URL}/chat`, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      timeout: 30000
    });

    return {
      success: true,
      data: response.data,
      status: response.status
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    };
  }
}

async function testChatSystemFixes() {
  console.log('🚀 COMPREHENSIVE CHAT SYSTEM TEST');
  console.log('Testing implemented fixes:\n');
  console.log('1. 🔍 Hybrid Search (vector + keyword fusion)');
  console.log('2. 🎯 Stage Override (force recommendations when candidates exist)');
  console.log('3. 🔑 Cache Key Fix (session-specific caching)');
  console.log('4. 🚀 Recommend-First Rule (prioritize recommendations over questions)\n');

  const results = {
    tests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      issues: []
    }
  };

  for (const testQuery of TEST_QUERIES) {
    console.log(`\n📝 Testing: ${testQuery.name}`);
    console.log(`Query: "${testQuery.message}"`);
    console.log(`Expected: ${testQuery.testType}`);

    const testResult = {
      name: testQuery.name,
      query: testQuery.message,
      expectedType: testQuery.testType,
      success: false,
      issues: [],
      response: null,
      analysis: {}
    };

    results.tests.push(testResult);
    results.summary.total++;

    // Send the chat message
    const chatResult = await sendChatMessage(testQuery.message);

    if (!chatResult.success) {
      console.log(`❌ FAIL: Request failed - ${chatResult.error}`);
      testResult.issues.push(`Request failed: ${chatResult.error}`);
      results.summary.failed++;
      continue;
    }

    const response = chatResult.data;
    testResult.response = response;

    console.log(`✅ Response received (${response.metadata?.response_time}ms)`);
    console.log(`   Provider: ${response.metadata?.llm_provider}`);
    console.log(`   Stage: ${response.conversation_stage}`);

    // Analyze the response
    const analysis = analyzeResponse(response, testQuery);
    testResult.analysis = analysis;

    // Check for fixes
    let fixesWorking = 0;
    let totalFixes = 4;

    // Fix 1: Stage Override
    if (response.conversation_stage === 'recommendation' || response.conversation_stage === 'discovery') {
      console.log(`   ✅ Stage Override: ${response.conversation_stage}`);
      fixesWorking++;
    } else {
      console.log(`   ❌ Stage Override: Unexpected stage ${response.conversation_stage}`);
      testResult.issues.push('Stage override not working properly');
    }

    // Fix 2: LLM Provider (should not be fallback for simple queries)
    if (response.metadata?.llm_provider && !response.metadata.llm_provider.includes('FALLBACK')) {
      console.log(`   ✅ LLM Provider: ${response.metadata.llm_provider}`);
      fixesWorking++;
    } else {
      console.log(`   ⚠️  LLM Provider: Using fallback (${response.metadata?.llm_provider})`);
      testResult.issues.push('Using fallback LLM provider');
    }

    // Fix 3: Response Quality (should contain relevant content)
    if (analysis.hasRelevantContent) {
      console.log(`   ✅ Response Quality: Contains relevant content`);
      fixesWorking++;
    } else {
      console.log(`   ❌ Response Quality: Generic/fallback response`);
      testResult.issues.push('Response appears to be generic fallback');
    }

    // Fix 4: Session Management
    if (response.session_id) {
      console.log(`   ✅ Session Management: ${response.session_id}`);
      fixesWorking++;
    } else {
      console.log(`   ❌ Session Management: No session ID`);
      testResult.issues.push('No session ID in response');
    }

    // Overall assessment
    const successRate = fixesWorking / totalFixes;
    if (successRate >= 0.75) {
      console.log(`   🎉 PASS: ${fixesWorking}/${totalFixes} fixes working (${Math.round(successRate * 100)}%)`);
      testResult.success = true;
      results.summary.passed++;
    } else {
      console.log(`   ⚠️  PARTIAL: ${fixesWorking}/${totalFixes} fixes working (${Math.round(successRate * 100)}%)`);
      results.summary.failed++;
    }

    // Show response preview
    console.log(`   Response: "${response.message?.substring(0, 100)}..."`);
  }

  return results;
}

function analyzeResponse(response, testQuery) {
  const analysis = {
    hasRelevantContent: false,
    hasToolRecommendations: false,
    hasDiscoveredEntities: false,
    responseType: 'unknown'
  };

  const message = response.message?.toLowerCase() || '';
  
  // Check for relevant content
  const hasKeywords = testQuery.expectedKeywords.some(keyword => 
    message.includes(keyword.toLowerCase())
  );
  
  const isGeneric = message.includes('hello') || 
                   message.includes('what kind of tasks') ||
                   message.includes('what specific features') ||
                   message.includes('i understand you\'re looking');

  analysis.hasRelevantContent = hasKeywords && !isGeneric;
  
  // Check for tool recommendations
  analysis.hasDiscoveredEntities = response.discoveredEntities && response.discoveredEntities.length > 0;
  analysis.hasToolRecommendations = analysis.hasDiscoveredEntities;

  // Determine response type
  if (analysis.hasToolRecommendations) {
    analysis.responseType = 'recommendations';
  } else if (isGeneric) {
    analysis.responseType = 'generic_fallback';
  } else if (message.includes('would you like') || message.includes('can you tell me')) {
    analysis.responseType = 'clarifying_questions';
  } else {
    analysis.responseType = 'contextual_response';
  }

  return analysis;
}

function printSummary(results) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 COMPREHENSIVE TEST SUMMARY');
  console.log('='.repeat(60));
  
  console.log(`\nOverall Results: ${results.summary.passed}/${results.summary.total} tests passed`);
  console.log(`Success Rate: ${Math.round((results.summary.passed / results.summary.total) * 100)}%`);

  console.log('\n🔧 IMPLEMENTED FIXES STATUS:');
  
  const fixStatus = {
    stageOverride: 0,
    llmProvider: 0,
    responseQuality: 0,
    sessionManagement: 0
  };

  results.tests.forEach(test => {
    if (test.response) {
      if (test.response.conversation_stage === 'recommendation' || test.response.conversation_stage === 'discovery') {
        fixStatus.stageOverride++;
      }
      if (test.response.metadata?.llm_provider && !test.response.metadata.llm_provider.includes('FALLBACK')) {
        fixStatus.llmProvider++;
      }
      if (test.analysis.hasRelevantContent) {
        fixStatus.responseQuality++;
      }
      if (test.response.session_id) {
        fixStatus.sessionManagement++;
      }
    }
  });

  const total = results.summary.total;
  console.log(`1. 🎯 Stage Override: ${fixStatus.stageOverride}/${total} (${Math.round((fixStatus.stageOverride/total)*100)}%)`);
  console.log(`2. 🤖 LLM Provider: ${fixStatus.llmProvider}/${total} (${Math.round((fixStatus.llmProvider/total)*100)}%)`);
  console.log(`3. 💬 Response Quality: ${fixStatus.responseQuality}/${total} (${Math.round((fixStatus.responseQuality/total)*100)}%)`);
  console.log(`4. 🔑 Session Management: ${fixStatus.sessionManagement}/${total} (${Math.round((fixStatus.sessionManagement/total)*100)}%)`);

  console.log('\n🎯 NEXT STEPS:');
  if (results.summary.passed === results.summary.total) {
    console.log('🎉 ALL FIXES ARE WORKING! Chat system is ready for production.');
  } else {
    console.log('🔧 Some fixes need attention:');
    
    if (fixStatus.llmProvider < total) {
      console.log('   • Check LLM provider configuration (API keys, fallback logic)');
    }
    if (fixStatus.responseQuality < total) {
      console.log('   • Verify entity discovery and hybrid search implementation');
    }
    if (fixStatus.stageOverride < total) {
      console.log('   • Review conversation stage transition logic');
    }
    if (fixStatus.sessionManagement < total) {
      console.log('   • Check session ID generation and management');
    }
  }
}

async function main() {
  const results = await testChatSystemFixes();
  printSummary(results);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testChatSystemFixes, sendChatMessage };
