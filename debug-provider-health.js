#!/usr/bin/env node

/**
 * Debug script to check LLM provider health status
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.x1nkcwYeYGVOplt1cAXsiPLvsGz8UNge1OSObjakRmo';

async function debugProviderHealth() {
  console.log('🔧 DEBUGGING LLM PROVIDER HEALTH\n');

  try {
    // Test 1: Send a chat message and capture detailed logs
    console.log('📝 Test 1: Sending chat message to trigger provider selection...');
    
    const chatResponse = await axios.post(`${BASE_URL}/chat`, {
      message: 'test provider health'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      timeout: 30000
    });

    console.log('✅ Chat response received');
    console.log('Provider used:', chatResponse.data.metadata?.llm_provider);
    console.log('Response stage:', chatResponse.data.conversation_stage);
    
    if (chatResponse.data.metadata?.llm_provider === 'OPENAI_FALLBACK') {
      console.log('🔴 CONFIRMED: System is using fallback instead of real providers');
    } else {
      console.log('✅ System is using real LLM providers');
    }

    // Test 2: Try multiple requests to see if pattern emerges
    console.log('\n📝 Test 2: Testing multiple requests...');
    
    const providers = [];
    for (let i = 0; i < 3; i++) {
      try {
        const response = await axios.post(`${BASE_URL}/chat`, {
          message: `test message ${i + 1}`
        }, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${AUTH_TOKEN}`
          },
          timeout: 15000
        });
        
        providers.push(response.data.metadata?.llm_provider);
        console.log(`Request ${i + 1}: ${response.data.metadata?.llm_provider}`);
      } catch (error) {
        console.log(`Request ${i + 1}: ERROR - ${error.message}`);
      }
    }

    // Analyze patterns
    const uniqueProviders = [...new Set(providers)];
    console.log('\n📊 Provider Usage Analysis:');
    console.log('Unique providers used:', uniqueProviders);
    
    if (uniqueProviders.length === 1 && uniqueProviders[0] === 'OPENAI_FALLBACK') {
      console.log('🔴 ISSUE: All requests use fallback - providers are unhealthy');
    } else if (uniqueProviders.includes('OPENAI')) {
      console.log('✅ GOOD: Real OpenAI provider is being used');
    }

    return {
      allFallback: uniqueProviders.length === 1 && uniqueProviders[0] === 'OPENAI_FALLBACK',
      providers: uniqueProviders
    };

  } catch (error) {
    console.log('❌ Error during provider health debug:', error.message);
    return { error: error.message };
  }
}

async function main() {
  const result = await debugProviderHealth();
  
  console.log('\n🎯 DIAGNOSIS:');
  
  if (result.error) {
    console.log('❌ Could not complete provider health check');
    console.log('Issue:', result.error);
  } else if (result.allFallback) {
    console.log('🔴 ROOT CAUSE IDENTIFIED: All LLM providers are marked as unhealthy');
    console.log('\n🔧 SOLUTIONS:');
    console.log('1. Restart the server to reset provider health state');
    console.log('2. Check server logs for provider error details');
    console.log('3. Verify OpenAI service configuration in the app');
    console.log('4. Add provider health reset endpoint');
  } else {
    console.log('✅ LLM providers are working correctly');
    console.log('Providers in use:', result.providers);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
