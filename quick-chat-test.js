#!/usr/bin/env node

/**
 * Quick diagnostic test for chat system
 * Tests if the server is running and basic chat functionality works
 */

const http = require('http');

function testServerHealth() {
  return new Promise((resolve, reject) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/healthz',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      resolve(res.statusCode === 200);
    });

    req.on('error', () => resolve(false));
    req.on('timeout', () => resolve(false));
    req.end();
  });
}

function testChatEndpoint() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      message: 'I need a tool for removing backgrounds from images'
    });

    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            error: 'Invalid JSON response',
            rawData: data
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        error: error.message
      });
    });

    req.on('timeout', () => {
      resolve({
        error: 'Request timeout'
      });
    });

    req.write(postData);
    req.end();
  });
}

async function main() {
  console.log('🔍 QUICK CHAT SYSTEM DIAGNOSTIC\n');

  // Test 1: Server Health
  console.log('1. Testing server health...');
  const isHealthy = await testServerHealth();
  
  if (!isHealthy) {
    console.log('❌ Server is not running or not healthy');
    console.log('💡 Start the server with: npm run start:dev');
    return;
  }
  
  console.log('✅ Server is running');

  // Test 2: Chat Endpoint
  console.log('\n2. Testing chat endpoint...');
  const chatResult = await testChatEndpoint();

  if (chatResult.error) {
    console.log('❌ Chat endpoint error:', chatResult.error);
    return;
  }

  if (chatResult.status !== 200) {
    console.log('❌ Chat endpoint returned status:', chatResult.status);
    console.log('Response:', chatResult.rawData);
    return;
  }

  console.log('✅ Chat endpoint is responding');

  // Test 3: Response Analysis
  console.log('\n3. Analyzing chat response...');
  const response = chatResult.data;

  if (!response.message) {
    console.log('❌ No message in response');
    return;
  }

  console.log('✅ Got chat message');
  console.log(`Message: ${response.message.substring(0, 100)}...`);

  // Check for recommendations
  if (response.discoveredEntities && response.discoveredEntities.length > 0) {
    console.log('✅ Got tool recommendations:');
    response.discoveredEntities.forEach(entity => {
      console.log(`  - ${entity.name} (relevance: ${entity.relevanceScore || 'N/A'})`);
    });
  } else {
    console.log('⚠️  No tool recommendations in response');
  }

  // Check for fallback patterns
  const message = response.message.toLowerCase();
  const isFallback = message.includes('i understand you\'re looking') ||
                    message.includes('what specific features') ||
                    message.includes('i apologize');

  if (isFallback) {
    console.log('⚠️  Response appears to be a fallback/discovery message');
    console.log('🔧 This suggests the fixes may not be fully working');
  } else {
    console.log('✅ Response appears to contain recommendations');
  }

  console.log('\n📊 SUMMARY');
  console.log('Server: ✅ Running');
  console.log('Chat Endpoint: ✅ Working');
  console.log(`Recommendations: ${response.discoveredEntities?.length > 0 ? '✅' : '⚠️'} ${response.discoveredEntities?.length || 0} tools`);
  console.log(`Response Type: ${isFallback ? '⚠️ Fallback' : '✅ Recommendations'}`);

  if (response.discoveredEntities?.length > 0 && !isFallback) {
    console.log('\n🎉 CHAT SYSTEM APPEARS TO BE WORKING!');
  } else {
    console.log('\n🔧 CHAT SYSTEM NEEDS MORE WORK');
    console.log('The fixes may not be fully effective yet.');
  }
}

if (require.main === module) {
  main().catch(console.error);
}
