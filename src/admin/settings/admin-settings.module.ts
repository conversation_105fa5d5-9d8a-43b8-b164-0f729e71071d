import { Module } from '@nestjs/common';
import { AdminSettingsController } from './admin-settings.controller';
import { AdminSettingsService } from './admin-settings.service';
import { PrismaModule } from '../../prisma/prisma.module';
import { AuthModule } from '../../auth/auth.module';
import { SupabaseModule } from '../../supabase/supabase.module'; // For SupabaseAuthGuard dependency
import { ChatModule } from '../../chat/chat.module'; // For LlmFailoverService

@Module({
  imports: [
    PrismaModule,
    AuthModule,
    SupabaseModule, // For SupabaseService dependency in SupabaseAuthGuard
    ChatModule, // For LlmFailoverService
  ],
  controllers: [AdminSettingsController],
  providers: [AdminSettingsService],
  exports: [AdminSettingsService],
})
export class AdminSettingsModule {}
