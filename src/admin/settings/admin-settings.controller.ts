import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { AdminSettingsService } from './admin-settings.service';
import { UpdateLlmProviderDto } from './dto/update-llm-provider.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { AppSettingResponseDto } from './dto/app-setting-response.dto';
import { LlmFailoverService } from '../../chat/services/llm-failover.service';

@ApiTags('Admin Settings')
@Controller('admin/settings')
@UseGuards(SupabaseAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminSettingsController {
  constructor(
    private readonly adminSettingsService: AdminSettingsService,
    private readonly llmFailoverService: LlmFailoverService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all application settings (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'List of all application settings',
    type: [AppSettingResponseDto],
  })
  async getAllSettings(): Promise<AppSettingResponseDto[]> {
    return this.adminSettingsService.getAllSettings();
  }

  @Get('llm')
  @ApiOperation({ summary: 'Get current LLM provider setting (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Current LLM provider setting',
    type: AppSettingResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'LLM provider setting not found',
  })
  async getLlmProvider(): Promise<AppSettingResponseDto> {
    return this.adminSettingsService.getLlmProvider();
  }

  @Put('llm')
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 requests per minute
  @ApiOperation({ summary: 'Update LLM provider setting (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'LLM provider setting updated successfully',
    type: AppSettingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid LLM provider',
  })
  @HttpCode(HttpStatus.OK)
  async updateLlmProvider(
    @Body() updateLlmProviderDto: UpdateLlmProviderDto,
  ): Promise<AppSettingResponseDto> {
    return this.adminSettingsService.updateLlmProvider(updateLlmProviderDto);
  }

  @Get(':key')
  @ApiOperation({ summary: 'Get a specific setting by key (Admin only)' })
  @ApiParam({
    name: 'key',
    description: 'The setting key',
    example: 'CURRENT_LLM_PROVIDER',
  })
  @ApiResponse({
    status: 200,
    description: 'Setting retrieved successfully',
    type: AppSettingResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Setting not found',
  })
  async getSetting(@Param('key') key: string): Promise<AppSettingResponseDto> {
    return this.adminSettingsService.getSetting(key);
  }

  @Put(':key')
  @Throttle({ default: { limit: 20, ttl: 60000 } }) // 20 requests per minute
  @ApiOperation({ summary: 'Update a specific setting by key (Admin only)' })
  @ApiParam({
    name: 'key',
    description: 'The setting key',
    example: 'CURRENT_LLM_PROVIDER',
  })
  @ApiResponse({
    status: 200,
    description: 'Setting updated successfully',
    type: AppSettingResponseDto,
  })
  @HttpCode(HttpStatus.OK)
  async updateSetting(
    @Param('key') key: string,
    @Body() updateSettingDto: UpdateSettingDto,
  ): Promise<AppSettingResponseDto> {
    return this.adminSettingsService.updateSetting(
      key,
      updateSettingDto.value,
      updateSettingDto.description,
    );
  }

  @Delete(':key')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
  @ApiOperation({ summary: 'Delete a setting by key (Admin only)' })
  @ApiParam({
    name: 'key',
    description: 'The setting key',
    example: 'SOME_CUSTOM_SETTING',
  })
  @ApiResponse({
    status: 204,
    description: 'Setting deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Setting not found',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteSetting(@Param('key') key: string): Promise<void> {
    return this.adminSettingsService.deleteSetting(key);
  }

  @Post('llm/reset')
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
  @ApiOperation({ summary: 'Reset all LLM provider health status (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'LLM provider health status reset successfully',
  })
  @HttpCode(HttpStatus.OK)
  async resetLlmProviderHealth(): Promise<{ message: string; status: Record<string, any> }> {
    this.llmFailoverService.resetAllProviderHealth();
    const status = this.llmFailoverService.getProviderHealthStatus();
    return {
      message: 'All LLM provider health status reset successfully',
      status
    };
  }

  @Get('llm/health')
  @ApiOperation({ summary: 'Get LLM provider health status (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'LLM provider health status retrieved successfully',
  })
  async getLlmProviderHealth(): Promise<{ status: Record<string, any> }> {
    const status = this.llmFailoverService.getProviderHealthStatus();
    return { status };
  }
}
