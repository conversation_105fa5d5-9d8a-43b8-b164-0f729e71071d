import { Injectable, Logger } from '@nestjs/common';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import {
  ILlmService,
  ConversationContext,
  ChatResponse,
  UserIntent,
  CandidateEntity,
} from '../../common/llm/interfaces/llm.service.interface';

/**
 * Service that provides automatic failover between LLM providers
 * Ensures high availability and graceful degradation
 */
@Injectable()
export class LlmFailoverService {
  private readonly logger = new Logger(LlmFailoverService.name);
  private readonly maxRetries = 3;
  private readonly retryDelayMs = 1000;
  
  // Track provider health and failures
  private providerHealth: Map<string, {
    isHealthy: boolean;
    lastFailure: Date | null;
    consecutiveFailures: number;
    lastSuccess: Date | null;
  }> = new Map();

  constructor(private readonly llmFactoryService: LlmFactoryService) {
    this.initializeProviderHealth();
  }

  /**
   * Get chat response with automatic failover
   */
  async getChatResponseWithFailover(
    userMessage: string,
    context: ConversationContext,
    candidateEntities?: CandidateEntity[],
  ): Promise<ChatResponse> {
    const availableProviders = this.getHealthyProviders();
    
    for (const provider of availableProviders) {
      try {
        this.logger.debug(`Attempting chat response with provider: ${provider}`);

        // 🚨 DIAGNOSTIC: Log provider attempt with payload details
        this.logger.log(`🔄 TRYING PROVIDER: ${provider}`, {
          candidateEntitiesCount: candidateEntities?.length || 0,
          hasEntities: !!candidateEntities && candidateEntities.length > 0,
          userMessage: userMessage.substring(0, 100) + '...',
          conversationStage: context.conversationStage,
          sessionId: context.sessionId
        });

        const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
        const response = await this.executeWithTimeout(
          () => llmService.getChatResponse(userMessage, context, candidateEntities),
          30000, // 30 second timeout
        );

        // 🚨 DIAGNOSTIC: Log successful response details
        this.logger.log(`✅ PROVIDER SUCCESS: ${provider}`, {
          responseMessageLength: response?.message?.length || 0,
          discoveredEntitiesCount: response?.discoveredEntities?.length || 0,
          hasDiscoveredEntities: !!response?.discoveredEntities && response.discoveredEntities.length > 0,
          sessionId: context.sessionId
        });

        // Mark provider as healthy on success
        this.markProviderHealthy(provider);

        this.logger.log(`Chat response successful with provider: ${provider}`);
        return response;
      } catch (error) {
        // 🔧 Enhanced error logging for LLM provider debugging
        this.logger.error(
          `[LLM_FAIL] ${provider} -> ${error.code ?? error?.response?.status ?? 'UNKNOWN'}`,
          {
            error: error.message,
            responseData: error.response?.data,
            statusCode: error.response?.status,
            sessionId: context.sessionId,
            provider,
          }
        );

        this.markProviderUnhealthy(provider, error);

        // Continue to next provider
        continue;
      }
    }

    // All providers failed
    throw new Error('All LLM providers failed to generate chat response');
  }

  /**
   * Classify intent with automatic failover
   */
  async classifyIntentWithFailover(
    userMessage: string,
    context: ConversationContext,
  ): Promise<UserIntent> {
    const availableProviders = this.getHealthyProviders();
    
    for (const provider of availableProviders) {
      try {
        this.logger.debug(`Attempting intent classification with provider: ${provider}`);
        
        const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
        const intent = await this.executeWithTimeout(
          () => llmService.classifyIntent(userMessage, context),
          15000, // 15 second timeout
        );

        this.markProviderHealthy(provider);
        return intent;
      } catch (error) {
        this.logger.warn(`Provider ${provider} failed for intent classification`, {
          error: error.message,
          sessionId: context.sessionId,
        });
        
        this.markProviderUnhealthy(provider, error);
        continue;
      }
    }

    // Return fallback intent if all providers fail
    this.logger.warn('All providers failed for intent classification, using fallback');
    return {
      type: 'discovery',
      confidence: 0.5,
      entities: [],
      categories: [],
      features: [],
      constraints: {},
    };
  }

  /**
   * Generate follow-up questions with failover
   */
  async generateFollowUpQuestionsWithFailover(
    context: ConversationContext,
  ): Promise<string[]> {
    const availableProviders = this.getHealthyProviders();
    
    for (const provider of availableProviders) {
      try {
        const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
        const questions = await this.executeWithTimeout(
          () => llmService.generateFollowUpQuestions(context),
          10000, // 10 second timeout
        );

        this.markProviderHealthy(provider);
        return questions;
      } catch (error) {
        this.markProviderUnhealthy(provider, error);
        continue;
      }
    }

    // Return fallback questions
    return this.getFallbackFollowUpQuestions(context);
  }

  /**
   * Check if should transition to recommendations with failover
   */
  async shouldTransitionToRecommendationsWithFailover(
    context: ConversationContext,
  ): Promise<{ shouldTransition: boolean; reason: string }> {
    const availableProviders = this.getHealthyProviders();
    
    for (const provider of availableProviders) {
      try {
        const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
        const result = await this.executeWithTimeout(
          () => llmService.shouldTransitionToRecommendations(context),
          10000,
        );

        this.markProviderHealthy(provider);
        return result;
      } catch (error) {
        this.markProviderUnhealthy(provider, error);
        continue;
      }
    }

    // Fallback logic for transition decision
    return this.getFallbackTransitionDecision(context);
  }

  /**
   * Get provider health status
   */
  getProviderHealthStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [provider, health] of this.providerHealth.entries()) {
      status[provider] = {
        isHealthy: health.isHealthy,
        consecutiveFailures: health.consecutiveFailures,
        lastFailure: health.lastFailure,
        lastSuccess: health.lastSuccess,
      };
    }

    return status;
  }

  /**
   * Manually mark a provider as healthy (for admin operations)
   */
  resetProviderHealth(provider: string): void {
    if (this.providerHealth.has(provider)) {
      this.markProviderHealthy(provider);
      this.logger.log(`Manually reset health for provider: ${provider}`);
    }
  }

  /**
   * Reset all provider health (circuit breaker reset)
   */
  resetAllProviderHealth(): void {
    for (const provider of this.providerHealth.keys()) {
      this.markProviderHealthy(provider);
    }
    this.logger.log('🔧 Circuit breaker reset: All providers marked healthy');
  }

  // Private helper methods
  private initializeProviderHealth(): void {
    const providers = this.llmFactoryService.getAvailableProviders();
    
    for (const provider of providers) {
      this.providerHealth.set(provider, {
        isHealthy: true,
        lastFailure: null,
        consecutiveFailures: 0,
        lastSuccess: null,
      });
    }

    this.logger.log(`Initialized health tracking for ${providers.length} LLM providers`);
  }

  private getHealthyProviders(): string[] {
    const allProviders = this.llmFactoryService.getAvailableProviders();
    const healthyProviders: string[] = [];
    const unhealthyProviders: string[] = [];

    for (const provider of allProviders) {
      const health = this.providerHealth.get(provider);
      
      if (health?.isHealthy) {
        healthyProviders.push(provider);
      } else {
        unhealthyProviders.push(provider);
        
        // Check if provider should be retried (after cooldown period)
        if (health && this.shouldRetryProvider(health)) {
          healthyProviders.push(provider);
          this.logger.debug(`Retrying provider ${provider} after cooldown`);
        }
      }
    }

    // If no healthy providers, try all providers as last resort
    if (healthyProviders.length === 0) {
      this.logger.warn('No healthy providers available, trying all providers');
      return allProviders;
    }

    return healthyProviders;
  }

  private shouldRetryProvider(health: any): boolean {
    if (!health.lastFailure) return true;
    
    const cooldownMs = Math.min(
      this.retryDelayMs * Math.pow(2, health.consecutiveFailures),
      300000 // Max 5 minutes
    );
    
    return Date.now() - health.lastFailure.getTime() > cooldownMs;
  }

  private markProviderHealthy(provider: string): void {
    const health = this.providerHealth.get(provider);
    if (health) {
      health.isHealthy = true;
      health.consecutiveFailures = 0;
      health.lastSuccess = new Date();
      this.providerHealth.set(provider, health);
    }
  }

  private markProviderUnhealthy(provider: string, error: any): void {
    const health = this.providerHealth.get(provider);
    if (health) {
      health.isHealthy = false;
      health.lastFailure = new Date();
      health.consecutiveFailures += 1;
      this.providerHealth.set(provider, health);
      
      this.logger.warn(`Provider ${provider} marked unhealthy`, {
        consecutiveFailures: health.consecutiveFailures,
        error: error.message,
      });
    }
  }

  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeoutMs: number,
  ): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timeout')), timeoutMs)
      ),
    ]);
  }

  private getFallbackFollowUpQuestions(context: ConversationContext): string[] {
    const questionsByStage = {
      greeting: [
        "What type of work or projects are you working on?",
        "Are you looking for tools for business or personal use?"
      ],
      discovery: [
        "What's your experience level with AI tools?",
        "Do you have a budget in mind?",
        "What's the main challenge you're trying to solve?"
      ],
      refinement: [
        "Would you prefer free tools or are you open to paid options?",
        "How technical do you want the tool to be?"
      ],
      recommendation: [
        "Would you like to see some specific recommendations?",
        "Should I focus on the most popular options?"
      ],
      comparison: [
        "What criteria are most important for your decision?",
        "Would you like me to compare pricing or features?"
      ]
    };

    return questionsByStage[context.conversationStage] || questionsByStage.discovery;
  }

  private getFallbackTransitionDecision(
    context: ConversationContext,
  ): { shouldTransition: boolean; reason: string } {
    // Simple heuristic-based decision
    const messageCount = context.messages.length;
    const entitiesDiscovered = context.discoveredEntities.length;
    const hasPreferences = Object.keys(context.userPreferences).length > 0;

    if (messageCount >= 6 && entitiesDiscovered >= 3 && hasPreferences) {
      return {
        shouldTransition: true,
        reason: 'Sufficient conversation depth and entity discovery for recommendations',
      };
    }

    return {
      shouldTransition: false,
      reason: 'Need more conversation context before transitioning to recommendations',
    };
  }
}
