/**
 * <PERSON><PERSON><PERSON> to get an authentication token for testing
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function getAuthToken() {
  console.log('🔑 Attempting to get authentication token...\n');
  
  // Try to create a test user
  const testUser = {
    email: `test.${Date.now()}@example.com`,
    password: 'TestPassword123!',
    username: `testuser${Date.now()}`,
    display_name: 'Test User'
  };
  
  try {
    console.log('📝 Step 1: Creating test user...');
    console.log('Email:', testUser.email);
    console.log('Username:', testUser.username);
    
    const signupResponse = await axios.post(
      `${BASE_URL}/auth/signup`,
      testUser,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ User created successfully:', signupResponse.data);
    
    // Wait a moment then try to login
    console.log('\n📝 Step 2: Attempting to login...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const loginResponse = await axios.post(
      `${BASE_URL}/auth/login`,
      {
        email: testUser.email,
        password: testUser.password
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ Login successful!');
    console.log('🔑 JWT Token:', loginResponse.data.access_token);
    
    return loginResponse.data.access_token;
    
  } catch (error) {
    console.log('❌ Error during authentication:', error.response?.status, error.response?.data);
    
    if (error.response?.status === 500 && error.response?.data?.message?.includes('Supabase')) {
      console.log('\n💡 It looks like there might be an issue with Supabase user creation.');
      console.log('This could be due to:');
      console.log('- Email confirmation requirements');
      console.log('- Supabase configuration');
      console.log('- Rate limiting');
      
      console.log('\n🔍 Alternative approaches:');
      console.log('1. Use the browser to manually create an account');
      console.log('2. Check if there are existing test credentials');
      console.log('3. Use the admin account if available');
    }
    
    return null;
  }
}

async function testWithExistingCredentials() {
  console.log('\n🔄 Trying with potential existing credentials...\n');
  
  // Try some common test credentials
  const testCredentials = [
    { email: '<EMAIL>', password: 'admin123' },
    { email: '<EMAIL>', password: 'TestPassword123!' },
    { email: '<EMAIL>', password: 'admin123' }
  ];
  
  for (const creds of testCredentials) {
    try {
      console.log(`📝 Trying login with: ${creds.email}`);
      
      const loginResponse = await axios.post(
        `${BASE_URL}/auth/login`,
        creds,
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
      
      console.log('✅ Login successful!');
      console.log('🔑 JWT Token:', loginResponse.data.access_token);
      
      return loginResponse.data.access_token;
      
    } catch (error) {
      console.log(`❌ Failed: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    }
  }
  
  return null;
}

async function main() {
  // First try to create a new user
  let token = await getAuthToken();
  
  // If that fails, try existing credentials
  if (!token) {
    token = await testWithExistingCredentials();
  }
  
  if (token) {
    console.log('\n🎉 SUCCESS! You can now test the chat endpoint.');
    console.log('Copy this token to test-chat-fix.js:');
    console.log(`const JWT_TOKEN = '${token}';`);
  } else {
    console.log('\n❌ Could not obtain authentication token.');
    console.log('You may need to manually create an account through the web interface.');
  }
}

main();
