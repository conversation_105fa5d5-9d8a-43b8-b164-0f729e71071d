#!/usr/bin/env node

/**
 * Test script to validate chat system fixes
 * Tests the three representative queries to identify where tools disappear
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
const TEST_QUERIES = [
  "teacher?",
  "spreadsheet free trial?", 
  "cheapest video generator"
];

// JWT token for testing
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3717khy7yMNKYgKQtkBr7nnSjyiuOJ3xLpWI9p5CaEg';

async function testChatQuery(query, sessionId = null) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🧪 TESTING QUERY: "${query}"`);
  console.log(`${'='.repeat(80)}`);
  
  try {
    const payload = {
      message: query,
      ...(sessionId && { session_id: sessionId })
    };

    const response = await fetch(`${BASE_URL}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.error(`Error details: ${errorText}`);
      return null;
    }

    const result = await response.json();
    
    console.log(`✅ RESPONSE RECEIVED:`);
    console.log(`📝 Message: ${result.message?.substring(0, 200)}...`);
    console.log(`🎯 Session ID: ${result.session_id}`);
    console.log(`📊 Conversation Stage: ${result.conversation_stage}`);
    console.log(`🔍 Discovered Entities: ${result.discovered_entities?.length || 0}`);
    console.log(`❓ Follow-up Questions: ${result.follow_up_questions?.length || 0}`);
    console.log(`🎬 Suggested Actions: ${result.suggested_actions?.length || 0}`);
    console.log(`🔄 Should Transition: ${result.should_transition_to_recommendations}`);
    
    if (result.discovered_entities?.length > 0) {
      console.log(`\n🎯 DISCOVERED ENTITIES:`);
      result.discovered_entities.forEach((entity, i) => {
        console.log(`  ${i+1}. ${entity.name} (Score: ${entity.relevance_score}) - ${entity.reason}`);
      });
    }

    if (result.suggested_actions?.length > 0) {
      console.log(`\n🎬 SUGGESTED ACTIONS:`);
      result.suggested_actions.forEach((action, i) => {
        console.log(`  ${i+1}. ${action.type}: ${action.label}`);
        if (action.data?.explanation) {
          console.log(`     Explanation: ${JSON.stringify(action.data.explanation, null, 2)}`);
        }
      });
    }

    console.log(`\n📊 METADATA:`);
    console.log(`  Response Time: ${result.metadata?.response_time}ms`);
    console.log(`  LLM Provider: ${result.metadata?.llm_provider}`);
    console.log(`  Candidates Provided: ${result.metadata?.candidateEntitiesProvided || 'N/A'}`);
    console.log(`  Entities Recommended: ${result.metadata?.entitiesRecommended || 'N/A'}`);
    console.log(`  Search Quality: ${result.metadata?.searchQuality || 'N/A'}`);

    return result;
    
  } catch (error) {
    console.error(`❌ REQUEST FAILED: ${error.message}`);
    return null;
  }
}

async function runTests() {
  console.log(`🚀 STARTING CHAT SYSTEM DIAGNOSTIC TESTS`);
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`🔑 Auth Token: ${AUTH_TOKEN.substring(0, 20)}...`);
  
  // JWT token is now set, ready to test

  const results = [];
  
  for (const query of TEST_QUERIES) {
    const result = await testChatQuery(query);
    results.push({ query, result });
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  console.log(`\n${'='.repeat(80)}`);
  console.log(`📊 TEST SUMMARY`);
  console.log(`${'='.repeat(80)}`);
  
  results.forEach(({ query, result }, i) => {
    console.log(`\n${i+1}. Query: "${query}"`);
    if (result) {
      console.log(`   ✅ Success: ${result.discovered_entities?.length || 0} entities found`);
      console.log(`   🎯 Stage: ${result.conversation_stage}`);
      console.log(`   🔄 Transition Ready: ${result.should_transition_to_recommendations}`);
    } else {
      console.log(`   ❌ Failed`);
    }
  });

  console.log(`\n🔍 DIAGNOSTIC CHECKLIST:`);
  console.log(`1. Check server logs for entity discovery results (candidateEntities.length)`);
  console.log(`2. Look for prompt size logs (should be > 2KB)`);
  console.log(`3. Verify LLM response parsing logs`);
  console.log(`4. Check for fallback to keyword search if vector search fails`);
  console.log(`5. Ensure no "I apologize, but I'm having trouble..." responses`);
}

// Run the tests
runTests().catch(console.error);
