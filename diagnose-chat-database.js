#!/usr/bin/env node

/**
 * Diagnostic script to test chat database functionality
 * This will help identify the root cause of conversation storage issues
 */

const { PrismaClient } = require('./generated/prisma');

async function diagnoseChatDatabase() {
  const prisma = new PrismaClient();
  
  console.log('🔍 CHAT DATABASE DIAGNOSTIC STARTING...\n');
  
  try {
    // Test 1: Basic database connection
    console.log('1️⃣ Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');
    
    // Test 2: Check if conversation_sessions table exists
    console.log('2️⃣ Checking conversation_sessions table...');
    try {
      const tableCheck = await prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'conversation_sessions'
        );
      `;
      console.log('✅ conversation_sessions table exists:', tableCheck[0].exists);
    } catch (error) {
      console.log('❌ Error checking table existence:', error.message);
    }
    
    // Test 3: Try to query conversation_sessions
    console.log('\n3️⃣ Testing conversation_sessions queries...');
    try {
      const sessionCount = await prisma.conversationSession.count();
      console.log('✅ Current conversation sessions count:', sessionCount);
    } catch (error) {
      console.log('❌ Error querying conversation_sessions:', error.message);
      console.log('Full error:', error);
    }
    
    // Test 4: Test conversation storage/retrieval
    console.log('\n4️⃣ Testing conversation storage/retrieval...');
    const testSessionId = 'diagnostic_test_' + Date.now();
    const testUserId = '00000000-0000-0000-0000-000000000001'; // Test UUID
    
    try {
      // Create test conversation
      const testContext = {
        userId: testUserId,
        sessionId: testSessionId,
        messages: [
          { role: 'user', content: 'Test message for diagnostics' },
          { role: 'assistant', content: 'Test response for diagnostics' }
        ],
        metadata: {
          createdAt: new Date().toISOString(),
          lastUpdated: new Date().toISOString()
        }
      };
      
      console.log('   Creating test conversation session...');
      await prisma.conversationSession.create({
        data: {
          sessionId: testSessionId,
          userId: testUserId,
          contextData: testContext,
          createdAt: new Date(),
          updatedAt: new Date(),
          expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
        }
      });
      console.log('   ✅ Test conversation created successfully');
      
      // Retrieve test conversation
      console.log('   Retrieving test conversation...');
      const retrieved = await prisma.conversationSession.findFirst({
        where: { sessionId: testSessionId }
      });
      
      if (retrieved) {
        console.log('   ✅ Test conversation retrieved successfully');
        console.log('   📊 Retrieved data structure:');
        console.log('      - Session ID:', retrieved.sessionId);
        console.log('      - User ID:', retrieved.userId);
        console.log('      - Context Data Type:', typeof retrieved.contextData);
        console.log('      - Messages Count:', retrieved.contextData?.messages?.length || 'N/A');
        console.log('      - Created At:', retrieved.createdAt);
        console.log('      - Expires At:', retrieved.expiresAt);
      } else {
        console.log('   ❌ Failed to retrieve test conversation');
      }
      
      // Clean up test data
      console.log('   Cleaning up test data...');
      await prisma.conversationSession.delete({
        where: { sessionId: testSessionId }
      });
      console.log('   ✅ Test data cleaned up');
      
    } catch (error) {
      console.log('   ❌ Error in conversation storage/retrieval test:', error.message);
      console.log('   Full error:', error);
    }
    
    // Test 5: Check for existing conversations
    console.log('\n5️⃣ Checking existing conversations...');
    try {
      const recentSessions = await prisma.conversationSession.findMany({
        take: 5,
        orderBy: { updatedAt: 'desc' },
        select: {
          sessionId: true,
          userId: true,
          createdAt: true,
          updatedAt: true,
          expiresAt: true
        }
      });
      
      console.log('✅ Recent conversation sessions:');
      if (recentSessions.length === 0) {
        console.log('   📭 No existing conversation sessions found');
      } else {
        recentSessions.forEach((session, index) => {
          console.log(`   ${index + 1}. Session: ${session.sessionId}`);
          console.log(`      User: ${session.userId}`);
          console.log(`      Created: ${session.createdAt}`);
          console.log(`      Updated: ${session.updatedAt}`);
          console.log(`      Expires: ${session.expiresAt}`);
          console.log('');
        });
      }
    } catch (error) {
      console.log('❌ Error checking existing conversations:', error.message);
    }
    
    // Test 6: Check database schema version
    console.log('6️⃣ Checking database schema...');
    try {
      const migrations = await prisma.$queryRaw`
        SELECT * FROM _prisma_migrations 
        ORDER BY finished_at DESC 
        LIMIT 5;
      `;
      console.log('✅ Recent migrations:');
      migrations.forEach((migration, index) => {
        console.log(`   ${index + 1}. ${migration.migration_name} - ${migration.finished_at}`);
      });
    } catch (error) {
      console.log('❌ Error checking migrations:', error.message);
    }
    
  } catch (error) {
    console.log('❌ CRITICAL ERROR:', error.message);
    console.log('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
  
  console.log('\n🏁 DIAGNOSTIC COMPLETE');
}

// Run the diagnostic
diagnoseChatDatabase().catch(console.error);
