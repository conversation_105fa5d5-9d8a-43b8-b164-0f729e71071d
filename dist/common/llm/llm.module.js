"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_module_1 = require("../../prisma/prisma.module");
const openai_module_1 = require("../../openai/openai.module");
const google_gemini_llm_service_1 = require("./services/google-gemini-llm.service");
const anthropic_llm_service_1 = require("./services/anthropic-llm.service");
const enhanced_anthropic_llm_service_1 = require("../../chat/services/enhanced-anthropic-llm.service");
const llm_factory_service_1 = require("./services/llm-factory.service");
const shared_prompt_builder_service_1 = require("./services/shared-prompt-builder.service");
let LlmModule = class LlmModule {
};
exports.LlmModule = LlmModule;
exports.LlmModule = LlmModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            prisma_module_1.PrismaModule,
            openai_module_1.OpenaiModule,
        ],
        providers: [
            shared_prompt_builder_service_1.SharedPromptBuilderService,
            google_gemini_llm_service_1.GoogleGeminiLlmService,
            anthropic_llm_service_1.AnthropicLlmService,
            enhanced_anthropic_llm_service_1.EnhancedAnthropicLlmService,
            llm_factory_service_1.LlmFactoryService,
            {
                provide: 'ILlmService',
                useFactory: async (llmFactory) => {
                    return llmFactory.getLlmService();
                },
                inject: [llm_factory_service_1.LlmFactoryService],
            },
        ],
        exports: ['ILlmService', llm_factory_service_1.LlmFactoryService],
    })
], LlmModule);
//# sourceMappingURL=llm.module.js.map