export interface LlmRecommendation {
    recommendedEntityIds: string[];
    explanation: string;
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface UserIntent {
    type: 'discovery' | 'comparison' | 'specific_tool' | 'general_question' | 'refinement';
    confidence: number;
    entities?: string[];
    categories?: string[];
    features?: string[];
    constraints?: {
        budget?: 'free' | 'low' | 'medium' | 'high';
        technical_level?: 'beginner' | 'intermediate' | 'advanced';
        use_case?: string;
    };
}
export interface EnhancedUserIntent extends UserIntent {
    extractedFilters: {
        entityTypeIds?: string[];
        searchTerm?: string;
        technical_levels?: string[];
        learning_curves?: string[];
        has_api?: boolean;
        has_free_tier?: boolean;
        open_source?: boolean;
        platforms?: string[];
        frameworks?: string[];
        use_cases_search?: string;
        key_features_search?: string;
        skill_levels?: string[];
        certificate_available?: boolean;
        instructor_name?: string;
        duration_text?: string;
        employment_types?: string[];
        experience_levels?: string[];
        location_types?: string[];
        salary_min?: number;
        salary_max?: number;
        company_name?: string;
        event_types?: string[];
        is_online?: boolean;
        location?: string;
        start_date_from?: string;
        start_date_to?: string;
        hardware_types?: string[];
        manufacturers?: string[];
        price_min?: number;
        price_max?: number;
        memory_search?: string;
        [key: string]: any;
    };
    filterConfidence: Record<string, number>;
    missingCriteria: {
        entityTypes?: boolean;
        technicalLevel?: boolean;
        budget?: boolean;
        useCase?: boolean;
        platform?: boolean;
        specificRequirements?: boolean;
    };
    clarifyingQuestions: SmartFollowUpQuestion[];
}
export interface SmartFollowUpQuestion {
    question: string;
    purpose: 'entity_type' | 'technical_level' | 'budget' | 'use_case' | 'platform' | 'refinement' | 'requirements';
    expectedFilterKeys: string[];
    priority: number;
    suggestedAnswers?: string[];
    followUpContext?: string;
}
export interface ConversationContext {
    sessionId: string;
    userId: string;
    messages: ChatMessage[];
    currentIntent?: UserIntent;
    discoveredEntities: string[];
    userPreferences: {
        budget?: 'free' | 'low' | 'medium' | 'high';
        technical_level?: 'beginner' | 'intermediate' | 'advanced';
        preferred_categories?: string[];
        excluded_categories?: string[];
    };
    conversationStage: 'greeting' | 'discovery' | 'refinement' | 'recommendation' | 'comparison';
    metadata: {
        startedAt: Date;
        lastActiveAt: Date;
        totalMessages: number;
        entitiesShown: string[];
    };
    conversationMemory?: ConversationMemory;
    discoveryProgress?: DiscoveryProgress;
    questionHistory?: QuestionHistory;
}
export interface ConversationMemory {
    userProfile: {
        industry?: string;
        role?: string;
        experienceLevel?: 'beginner' | 'intermediate' | 'advanced';
        teamSize?: 'individual' | 'small_team' | 'large_team' | 'enterprise';
        budget?: 'free' | 'low' | 'medium' | 'high' | 'enterprise';
        technicalSkills?: string[];
        primaryUseCases?: string[];
        workContext?: string;
    };
    requirements: {
        mustHave: string[];
        niceToHave: string[];
        dealBreakers: string[];
        specificFeatures: string[];
        integrationNeeds: string[];
        platformPreferences: string[];
    };
    discussedTopics: {
        entityTypes: string[];
        categories: string[];
        features: string[];
        useCases: string[];
        competitors: string[];
        concerns: string[];
    };
    insights: {
        primaryGoal: string;
        urgency: 'low' | 'medium' | 'high';
        decisionMakers: string[];
        evaluationCriteria: string[];
        timeline: string;
    };
}
export interface DiscoveryProgress {
    phase: 'initial' | 'exploration' | 'refinement' | 'evaluation' | 'decision';
    completedSteps: string[];
    nextSteps: string[];
    confidence: number;
    readinessScore: number;
    informationGathered: {
        useCase: boolean;
        industry: boolean;
        technicalLevel: boolean;
        budget: boolean;
        teamSize: boolean;
        timeline: boolean;
        specificRequirements: boolean;
        integrationNeeds: boolean;
    };
}
export interface QuestionHistory {
    askedQuestions: {
        question: string;
        timestamp: Date;
        category: string;
        answered: boolean;
        answer?: string;
        effectiveness: number;
    }[];
    questionCategories: {
        [category: string]: {
            count: number;
            lastAsked: Date;
            effectiveness: number;
            shouldAvoid: boolean;
        };
    };
    avoidedTopics: string[];
    preferredTopics: string[];
}
export interface EnhancedConversationContext extends ConversationContext {
    currentIntent?: EnhancedUserIntent;
    accumulatedFilters: {
        filters: Record<string, any>;
        confidence: Record<string, number>;
        history: Record<string, number>;
        source: Record<string, 'user_explicit' | 'extracted' | 'inferred' | 'clarified'>;
    };
    pendingClarifications: {
        filterKey: string;
        question: string;
        priority: number;
        askedAt?: Date;
    }[];
    enhancedMetadata: {
        filtersExtracted: number;
        clarificationQuestions: number;
        conversationQuality: number;
        readyForRecommendations: boolean;
        lastEntityDiscovery?: {
            timestamp: Date;
            filtersUsed: Record<string, any>;
            entitiesFound: number;
        };
    };
}
export interface ChatResponse {
    message: string;
    intent: UserIntent;
    suggestedActions?: Array<{
        type: 'ask_question' | 'show_entities' | 'refine_search' | 'get_recommendations';
        label: string;
        data?: any;
    }>;
    discoveredEntities?: Array<{
        id: string;
        name: string;
        relevanceScore: number;
        reason: string;
    }>;
    followUpQuestions?: string[];
    shouldTransitionToRecommendations: boolean;
    conversationStage: ConversationContext['conversationStage'];
    metadata: {
        responseTime: number;
        llmProvider: string;
        tokensUsed?: number;
    };
}
export interface CandidateEntity {
    id: string;
    name: string;
    shortDescription: string | null;
    description: string | null;
    entityType: {
        name: string;
        slug: string;
    };
    categories: Array<{
        category: {
            name: string;
            slug: string;
        };
    }>;
    tags: Array<{
        tag: {
            name: string;
            slug: string;
        };
    }>;
    features: Array<{
        feature: {
            name: string;
            slug: string;
        };
    }>;
    websiteUrl?: string | null;
    logoUrl?: string | null;
    avgRating?: number;
    reviewCount?: number;
    similarity?: number;
}
export interface ILlmService {
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    getChatResponse(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestions(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendations(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
}
