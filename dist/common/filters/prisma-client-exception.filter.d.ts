import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { PrismaClientKnownRequestError, PrismaClientValidationError } from '@prisma/client/runtime/library';
import { HttpAdapterHost } from '@nestjs/core';
import { AppLoggerService } from '../logger/logger.service';
export declare class PrismaClientExceptionFilter implements ExceptionFilter {
    private readonly httpAdapterHost;
    private readonly logger;
    constructor(httpAdapterHost: HttpAdapterHost, logger: AppLoggerService);
    catch(exception: PrismaClientKnownRequestError | PrismaClientValidationError, host: ArgumentsHost): void;
}
