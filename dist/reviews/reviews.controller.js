"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewsController = void 0;
const common_1 = require("@nestjs/common");
const reviews_service_1 = require("./reviews.service");
const create_review_dto_1 = require("./dto/create-review.dto");
const list_reviews_dto_1 = require("./dto/list-reviews.dto");
const update_review_dto_1 = require("./dto/update-review.dto");
const supabase_auth_guard_1 = require("../auth/guards/supabase-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
let ReviewsController = class ReviewsController {
    constructor(reviewsService) {
        this.reviewsService = reviewsService;
    }
    async submitReview(entityId, user, createReviewDto) {
        return this.reviewsService.createReview(user.id, entityId, createReviewDto);
    }
    async getApprovedReviewsForEntity(entityId, listReviewsDto) {
        return this.reviewsService.getApprovedReviewsForEntity(entityId, listReviewsDto);
    }
    async updateUserReview(reviewId, user, updateReviewDto) {
        return this.reviewsService.updateUserReview(reviewId, user.id, updateReviewDto);
    }
    async deleteUserReview(reviewId, user) {
        return this.reviewsService.deleteUserReview(reviewId, user.id);
    }
};
exports.ReviewsController = ReviewsController;
__decorate([
    (0, common_1.Post)('entities/:entityId/reviews'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Submit a new review for an entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', type: 'string', format: 'uuid', description: 'The ID of the entity to review' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Review submitted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Entity not found.' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'User has already reviewed this entity.' }),
    __param(0, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, create_review_dto_1.CreateReviewDto]),
    __metadata("design:returntype", Promise)
], ReviewsController.prototype, "submitReview", null);
__decorate([
    (0, common_1.Get)('entities/:entityId/reviews'),
    (0, swagger_1.ApiOperation)({ summary: 'Get approved reviews for an entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', type: 'string', format: 'uuid', description: 'The ID of the entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of approved reviews.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Entity not found.' }),
    __param(0, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, list_reviews_dto_1.ListReviewsDto]),
    __metadata("design:returntype", Promise)
], ReviewsController.prototype, "getApprovedReviewsForEntity", null);
__decorate([
    (0, common_1.Put)('reviews/:reviewId'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update an existing review by its ID' }),
    (0, swagger_1.ApiParam)({ name: 'reviewId', type: 'string', format: 'uuid', description: 'The ID of the review to update' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Review updated successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden. User cannot update this review.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Review not found.' }),
    __param(0, (0, common_1.Param)('reviewId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, update_review_dto_1.UpdateReviewDto]),
    __metadata("design:returntype", Promise)
], ReviewsController.prototype, "updateUserReview", null);
__decorate([
    (0, common_1.Delete)('reviews/:reviewId'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a review by its ID' }),
    (0, swagger_1.ApiParam)({ name: 'reviewId', type: 'string', format: 'uuid', description: 'The ID of the review to delete' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Review deleted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden. User cannot delete this review.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Review not found (or already deleted).' }),
    __param(0, (0, common_1.Param)('reviewId', common_1.ParseUUIDPipe)),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReviewsController.prototype, "deleteUserReview", null);
exports.ReviewsController = ReviewsController = __decorate([
    (0, swagger_1.ApiTags)('Reviews (User)'),
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [reviews_service_1.ReviewsService])
], ReviewsController);
//# sourceMappingURL=reviews.controller.js.map