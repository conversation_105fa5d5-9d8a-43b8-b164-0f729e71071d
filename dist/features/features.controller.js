"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeaturesController = void 0;
const common_1 = require("@nestjs/common");
const features_service_1 = require("./features.service");
const create_feature_dto_1 = require("./dto/create-feature.dto");
const update_feature_dto_1 = require("./dto/update-feature.dto");
const feature_response_dto_1 = require("./dto/feature-response.dto");
const admin_guard_1 = require("../auth/guards/admin.guard");
const supabase_auth_guard_1 = require("../auth/guards/supabase-auth.guard");
const swagger_1 = require("@nestjs/swagger");
let FeaturesController = class FeaturesController {
    constructor(featuresService) {
        this.featuresService = featuresService;
    }
    async findAllPublic() {
        const features = await this.featuresService.findAll();
        return features.map(this.mapToResponseDto);
    }
    async createAdmin(createFeatureDto) {
        const feature = await this.featuresService.create(createFeatureDto);
        return this.mapToResponseDto(feature);
    }
    async findAllAdmin() {
        const features = await this.featuresService.findAll();
        return features.map(this.mapToResponseDto);
    }
    async findOneAdmin(idOrSlug) {
        let feature;
        if (this.isUUID(idOrSlug)) {
            feature = await this.featuresService.findOne(idOrSlug);
        }
        else {
            feature = await this.featuresService.findBySlug(idOrSlug);
        }
        return this.mapToResponseDto(feature);
    }
    async updateAdmin(id, updateFeatureDto) {
        const feature = await this.featuresService.update(id, updateFeatureDto);
        return this.mapToResponseDto(feature);
    }
    async removeAdmin(id) {
        await this.featuresService.remove(id);
    }
    mapToResponseDto(feature) {
        return {
            id: feature.id,
            name: feature.name,
            slug: feature.slug,
            description: feature.description,
            iconUrl: feature.iconUrl,
            createdAt: feature.createdAt,
            updatedAt: feature.updatedAt,
        };
    }
    isUUID(str) {
        const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
        return uuidRegex.test(str);
    }
};
exports.FeaturesController = FeaturesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all features' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of all features',
        type: [feature_response_dto_1.FeatureResponseDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "findAllPublic", null);
__decorate([
    (0, common_1.Post)('/admin'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new feature (Admin)' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The feature has been successfully created.',
        type: feature_response_dto_1.FeatureResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_feature_dto_1.CreateFeatureDto]),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "createAdmin", null);
__decorate([
    (0, common_1.Get)('/admin'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all features (Admin)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of all features for admin dashboard',
        type: [feature_response_dto_1.FeatureResponseDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "findAllAdmin", null);
__decorate([
    (0, common_1.Get)('/admin/:idOrSlug'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific feature by ID or Slug (Admin)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Feature details', type: feature_response_dto_1.FeatureResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Feature not found' }),
    (0, swagger_1.ApiParam)({ name: 'idOrSlug', description: 'Feature ID (UUID) or slug', type: String }),
    __param(0, (0, common_1.Param)('idOrSlug')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "findOneAdmin", null);
__decorate([
    (0, common_1.Patch)('/admin/:id'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update a feature (Admin)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The feature has been successfully updated.',
        type: feature_response_dto_1.FeatureResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Feature not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Feature ID (UUID)', type: String }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_feature_dto_1.UpdateFeatureDto]),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "updateAdmin", null);
__decorate([
    (0, common_1.Delete)('/admin/:id'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a feature (Admin)' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'The feature has been successfully deleted.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Feature not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Feature ID (UUID)', type: String }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FeaturesController.prototype, "removeAdmin", null);
exports.FeaturesController = FeaturesController = __decorate([
    (0, swagger_1.ApiTags)('Features'),
    (0, common_1.Controller)('features'),
    __metadata("design:paramtypes", [features_service_1.FeaturesService])
], FeaturesController);
//# sourceMappingURL=features.controller.js.map