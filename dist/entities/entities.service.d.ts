import { OnModuleInit } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { Entity, Prisma, User as UserModel, EntityStatus } from 'generated/prisma';
import { PaginatedResponse } from '../common/interfaces/paginated-response.interface';
import { OpenaiService } from '../openai/openai.service';
import { ActivityLoggerService } from '../common/activity-logger.service';
import { ValidationService } from '../common/validation.service';
import { VectorSearchDto } from './dto/vector-search.dto';
export interface VectorSearchResult {
    id: string;
    name: string;
    shortDescription: string | null;
    logoUrl: string | null;
    entityTypeSlug: string;
    similarity: number;
}
export type EntityWithRelations = Prisma.EntityGetPayload<{
    include: {
        entityType: true;
        submitter: {
            select: {
                id: true;
                authUserId: true;
                email: true;
                createdAt: true;
                lastLogin: true;
                username: true;
                displayName: true;
                profilePictureUrl: true;
            };
        };
        entityCategories: {
            include: {
                category: true;
            };
        };
        entityTags: {
            include: {
                tag: true;
            };
        };
        entityFeatures: {
            include: {
                feature: true;
            };
        };
        reviews: {
            where: {
                status: 'APPROVED';
            };
            orderBy: {
                createdAt: 'desc';
            };
            include: {
                user: {
                    select: {
                        id: true;
                        username: true;
                        profilePictureUrl: true;
                    };
                };
                reviewVotes: true;
            };
        };
        entityDetailsTool: true;
        entityDetailsCourse: true;
        entityDetailsAgency: true;
        entityDetailsContentCreator: true;
        entityDetailsCommunity: true;
        entityDetailsNewsletter: true;
        entityDetailsDataset: true;
        entityDetailsResearchPaper: true;
        entityDetailsSoftware: true;
        entityDetailsModel: true;
        entityDetailsProjectReference: true;
        entityDetailsServiceProvider: true;
        entityDetailsInvestor: true;
        entityDetailsEvent: true;
        entityDetailsJob: true;
        entityDetailsGrant: true;
        entityDetailsBounty: true;
        entityDetailsHardware: true;
        entityDetailsNews: true;
        entityDetailsBook: true;
        entityDetailsPodcast: true;
        entityDetailsPlatform: true;
        _count: {
            select: {
                userSavedEntities: true;
                userUpvotes: true;
            };
        };
    };
}>;
export declare class EntitiesService implements OnModuleInit {
    private readonly prisma;
    private readonly openaiService;
    private readonly activityLogger;
    private readonly validationService;
    private entityTypeMap;
    private readonly logger;
    constructor(prisma: PrismaService, openaiService: OpenaiService, activityLogger: ActivityLoggerService, validationService: ValidationService);
    onModuleInit(): Promise<void>;
    private loadEntityTypes;
    private getUniqueSlug;
    generateFtsTextForEntity(entityId: string, tx: Prisma.TransactionClient): Promise<string>;
    private generateEmbeddingWithRetry;
    generateAndSaveEmbedding(entityId: string, tx: Prisma.TransactionClient): Promise<void>;
    backfillMissingEmbeddings(batchSize?: number): Promise<{
        processed: number;
        failed: string[];
    }>;
    private mapToolDetailsToPrisma;
    private mapCourseDetailsToPrisma;
    private mapAgencyDetailsToPrisma;
    private mapContentCreatorDetailsToPrisma;
    private mapCommunityDetailsToPrisma;
    private mapNewsletterDetailsToPrisma;
    private mapGenericDetailsToPrisma;
    private mapDetailsToPrisma;
    create(createEntityDto: CreateEntityDto, submitterUser: UserModel): Promise<Entity>;
    findAll(listEntitiesDto: ListEntitiesDto): Promise<PaginatedResponse<Entity>>;
    private getFindAllIncludes;
    findOne(id: string): Promise<Entity | null>;
    findBySlug(slug: string): Promise<Entity | null>;
    update(id: string, updateEntityDto: UpdateEntityDto, currentUser: UserModel): Promise<Entity>;
    adminSetStatus(id: string, newStatus: EntityStatus): Promise<Entity>;
    remove(id: string, user: UserModel): Promise<void>;
    vectorSearch(vectorSearchDto: VectorSearchDto): Promise<VectorSearchResult[]>;
    private buildOrderBy;
}
