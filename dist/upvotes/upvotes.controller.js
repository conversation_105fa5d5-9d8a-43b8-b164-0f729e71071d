"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpvotesController = void 0;
const common_1 = require("@nestjs/common");
const upvotes_service_1 = require("./upvotes.service");
const supabase_auth_guard_1 = require("../auth/guards/supabase-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const logger_service_1 = require("../common/logger/logger.service");
let UpvotesController = class UpvotesController {
    constructor(upvotesService, logger) {
        this.upvotesService = upvotesService;
        this.logger = logger;
    }
    async addUpvote(user, entityId) {
        if (!user?.id) {
            this.logger.logError(new Error('User ID missing in upvote request'), {
                operation: 'addUpvote',
                entityId,
                type: 'missing_user_id',
            });
            throw new common_1.BadRequestException('User authentication failed. Please log in again.');
        }
        this.logger.log('Upvote request received', {
            operation: 'addUpvote',
            userId: user.id,
            entityId,
        });
        return this.upvotesService.addUpvote(user.id, entityId);
    }
    async removeUpvote(user, entityId) {
        if (!user?.id) {
            this.logger.logError(new Error('User ID missing in upvote delete request'), {
                operation: 'removeUpvote',
                entityId,
                type: 'missing_user_id',
            });
            throw new common_1.BadRequestException('User authentication failed. Please log in again.');
        }
        this.logger.log('Remove upvote request received', {
            operation: 'removeUpvote',
            userId: user.id,
            entityId,
        });
        return this.upvotesService.removeUpvote(user.id, entityId);
    }
};
exports.UpvotesController = UpvotesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Upvote an entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', required: true, description: 'UUID of the entity to upvote', type: String, format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Entity upvoted successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Entity was already upvoted (idempotent operation).' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input or UUID format.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Entity not found.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UpvotesController.prototype, "addUpvote", null);
__decorate([
    (0, common_1.Delete)(),
    (0, swagger_1.ApiOperation)({ summary: 'Remove upvote from an entity' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', required: true, description: 'UUID of the entity to remove upvote from', type: String, format: 'uuid' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Upvote removed successfully.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid UUID format.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error.' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], UpvotesController.prototype, "removeUpvote", null);
exports.UpvotesController = UpvotesController = __decorate([
    (0, swagger_1.ApiTags)('Upvotes'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard),
    (0, common_1.Controller)('entities/:entityId/upvote'),
    __metadata("design:paramtypes", [upvotes_service_1.UpvotesService,
        logger_service_1.AppLoggerService])
], UpvotesController);
//# sourceMappingURL=upvotes.controller.js.map