"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AdminEntitiesController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminEntitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const supabase_auth_guard_1 = require("../../auth/guards/supabase-auth.guard");
const admin_guard_1 = require("../../auth/guards/admin.guard");
const entities_service_1 = require("../../entities/entities.service");
const admin_update_entity_status_dto_1 = require("./dto/admin-update-entity-status.dto");
let AdminEntitiesController = AdminEntitiesController_1 = class AdminEntitiesController {
    constructor(entitiesService) {
        this.entitiesService = entitiesService;
        this.logger = new common_1.Logger(AdminEntitiesController_1.name);
    }
    async updateEntityStatus(id, adminUpdateEntityStatusDto) {
        return this.entitiesService.adminSetStatus(id, adminUpdateEntityStatusDto.status);
    }
    async backfillMissingEmbeddings(batchSize) {
        this.logger.log(`[Admin] Starting embedding backfill with batch size: ${batchSize || 10}`);
        const result = await this.entitiesService.backfillMissingEmbeddings(batchSize || 10);
        const message = `Backfill completed: ${result.processed} entities processed successfully, ${result.failed.length} failed`;
        this.logger.log(`[Admin] ${message}`);
        if (result.failed.length > 0) {
            this.logger.warn(`[Admin] Failed entities:`, result.failed);
        }
        return {
            ...result,
            message
        };
    }
};
exports.AdminEntitiesController = AdminEntitiesController;
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Admin: Update entity status' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, admin_update_entity_status_dto_1.AdminUpdateEntityStatusDto]),
    __metadata("design:returntype", Promise)
], AdminEntitiesController.prototype, "updateEntityStatus", null);
__decorate([
    (0, common_1.Post)('backfill-embeddings'),
    (0, swagger_1.ApiOperation)({
        summary: 'Admin: Backfill missing embeddings',
        description: 'Generate embeddings for entities that don\'t have them. This is useful for fixing entities created before the embedding system was implemented.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'batchSize',
        required: false,
        type: Number,
        description: 'Number of entities to process in this batch (default: 10)',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Backfill process completed',
        schema: {
            type: 'object',
            properties: {
                processed: { type: 'number', description: 'Number of entities successfully processed' },
                failed: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'List of entities that failed to process with error messages'
                },
                message: { type: 'string', description: 'Summary message' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin role required' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Query)('batchSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminEntitiesController.prototype, "backfillMissingEmbeddings", null);
exports.AdminEntitiesController = AdminEntitiesController = AdminEntitiesController_1 = __decorate([
    (0, swagger_1.ApiTags)('Admin - Entities'),
    (0, common_1.Controller)('admin/entities'),
    (0, common_1.UseGuards)(supabase_auth_guard_1.SupabaseAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService])
], AdminEntitiesController);
//# sourceMappingURL=admin-entities.controller.js.map