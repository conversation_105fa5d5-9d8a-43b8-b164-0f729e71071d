import { AdminSettingsService } from './admin-settings.service';
import { UpdateLlmProviderDto } from './dto/update-llm-provider.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { AppSettingResponseDto } from './dto/app-setting-response.dto';
import { LlmFailoverService } from '../../chat/services/llm-failover.service';
export declare class AdminSettingsController {
    private readonly adminSettingsService;
    private readonly llmFailoverService;
    constructor(adminSettingsService: AdminSettingsService, llmFailoverService: LlmFailoverService);
    getAllSettings(): Promise<AppSettingResponseDto[]>;
    getLlmProvider(): Promise<AppSettingResponseDto>;
    updateLlmProvider(updateLlmProviderDto: UpdateLlmProviderDto): Promise<AppSettingResponseDto>;
    getSetting(key: string): Promise<AppSettingResponseDto>;
    updateSetting(key: string, updateSettingDto: UpdateSettingDto): Promise<AppSettingResponseDto>;
    deleteSetting(key: string): Promise<void>;
    resetLlmProviderHealth(): Promise<{
        message: string;
        status: Record<string, any>;
    }>;
    getLlmProviderHealth(): Promise<{
        status: Record<string, any>;
    }>;
}
