import { ConfigService } from '@nestjs/config';
import { ILlmService, LlmRecommendation, CandidateEntity, ChatResponse, ConversationContext, UserIntent } from '../../common/llm/interfaces/llm.service.interface';
export declare class EnhancedAnthropicLlmService implements ILlmService {
    private readonly configService;
    private readonly logger;
    private readonly apiKey;
    private readonly apiUrl;
    constructor(configService: ConfigService);
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    getChatResponse(userMessage: string, context: ConversationContext, candidateEntities?: CandidateEntity[]): Promise<ChatResponse>;
    classifyIntent(userMessage: string, context: ConversationContext): Promise<UserIntent>;
    generateFollowUpQuestions(context: ConversationContext): Promise<string[]>;
    shouldTransitionToRecommendations(context: ConversationContext): Promise<{
        shouldTransition: boolean;
        reason: string;
    }>;
    private preprocessConversationContext;
    private analyzeRepetition;
    private calculateStringSimilarity;
    private extractConversationTopics;
    private assessUserEngagement;
    private buildEnhancedChatPrompt;
    private buildEnhancedRecommendationPrompt;
    private buildEnhancedIntentClassificationPrompt;
    private buildEnhancedFollowUpPrompt;
    private buildEnhancedTransitionPrompt;
    private callAnthropicAPI;
    private parseEnhancedChatResponse;
    private validateDiscoveredEntities;
    private validateMessageContentForHallucination;
    private isGenericTerm;
    private getGenericReplacement;
    private validateAndEnhanceResponse;
    private getContextAwareFallbackMessage;
    private extractPreviousQuestions;
    private parseAnthropicResponse;
    private parseIntentResponse;
    private parseFollowUpResponse;
    private parseTransitionResponse;
    private formatEntitiesForChat;
    private formatUserProfile;
    private getEnhancedFallbackRecommendation;
    private getEnhancedFallbackChatResponse;
    private getEnhancedFallbackIntent;
    private getEnhancedFallbackFollowUpQuestions;
    private getEnhancedTransitionDecision;
}
