"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var LlmFailoverService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmFailoverService = void 0;
const common_1 = require("@nestjs/common");
const llm_factory_service_1 = require("../../common/llm/services/llm-factory.service");
let LlmFailoverService = LlmFailoverService_1 = class LlmFailoverService {
    constructor(llmFactoryService) {
        this.llmFactoryService = llmFactoryService;
        this.logger = new common_1.Logger(LlmFailoverService_1.name);
        this.maxRetries = 3;
        this.retryDelayMs = 1000;
        this.providerHealth = new Map();
        this.initializeProviderHealth();
    }
    async getChatResponseWithFailover(userMessage, context, candidateEntities) {
        const availableProviders = this.getHealthyProviders();
        for (const provider of availableProviders) {
            try {
                this.logger.debug(`Attempting chat response with provider: ${provider}`);
                this.logger.log(`🔄 TRYING PROVIDER: ${provider}`, {
                    candidateEntitiesCount: candidateEntities?.length || 0,
                    hasEntities: !!candidateEntities && candidateEntities.length > 0,
                    userMessage: userMessage.substring(0, 100) + '...',
                    conversationStage: context.conversationStage,
                    sessionId: context.sessionId
                });
                const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
                const response = await this.executeWithTimeout(() => llmService.getChatResponse(userMessage, context, candidateEntities), 30000);
                this.logger.log(`✅ PROVIDER SUCCESS: ${provider}`, {
                    responseMessageLength: response?.message?.length || 0,
                    discoveredEntitiesCount: response?.discoveredEntities?.length || 0,
                    hasDiscoveredEntities: !!response?.discoveredEntities && response.discoveredEntities.length > 0,
                    sessionId: context.sessionId
                });
                this.markProviderHealthy(provider);
                this.logger.log(`Chat response successful with provider: ${provider}`);
                return response;
            }
            catch (error) {
                this.logger.error(`[LLM_FAIL] ${provider} -> ${error.code ?? error?.response?.status ?? 'UNKNOWN'}`, {
                    error: error.message,
                    responseData: error.response?.data,
                    statusCode: error.response?.status,
                    sessionId: context.sessionId,
                    provider,
                });
                this.markProviderUnhealthy(provider, error);
                continue;
            }
        }
        throw new Error('All LLM providers failed to generate chat response');
    }
    async classifyIntentWithFailover(userMessage, context) {
        const availableProviders = this.getHealthyProviders();
        for (const provider of availableProviders) {
            try {
                this.logger.debug(`Attempting intent classification with provider: ${provider}`);
                const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
                const intent = await this.executeWithTimeout(() => llmService.classifyIntent(userMessage, context), 15000);
                this.markProviderHealthy(provider);
                return intent;
            }
            catch (error) {
                this.logger.warn(`Provider ${provider} failed for intent classification`, {
                    error: error.message,
                    sessionId: context.sessionId,
                });
                this.markProviderUnhealthy(provider, error);
                continue;
            }
        }
        this.logger.warn('All providers failed for intent classification, using fallback');
        return {
            type: 'discovery',
            confidence: 0.5,
            entities: [],
            categories: [],
            features: [],
            constraints: {},
        };
    }
    async generateFollowUpQuestionsWithFailover(context) {
        const availableProviders = this.getHealthyProviders();
        for (const provider of availableProviders) {
            try {
                const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
                const questions = await this.executeWithTimeout(() => llmService.generateFollowUpQuestions(context), 10000);
                this.markProviderHealthy(provider);
                return questions;
            }
            catch (error) {
                this.markProviderUnhealthy(provider, error);
                continue;
            }
        }
        return this.getFallbackFollowUpQuestions(context);
    }
    async shouldTransitionToRecommendationsWithFailover(context) {
        const availableProviders = this.getHealthyProviders();
        for (const provider of availableProviders) {
            try {
                const llmService = this.llmFactoryService.getLlmServiceByProvider(provider);
                const result = await this.executeWithTimeout(() => llmService.shouldTransitionToRecommendations(context), 10000);
                this.markProviderHealthy(provider);
                return result;
            }
            catch (error) {
                this.markProviderUnhealthy(provider, error);
                continue;
            }
        }
        return this.getFallbackTransitionDecision(context);
    }
    getProviderHealthStatus() {
        const status = {};
        for (const [provider, health] of this.providerHealth.entries()) {
            status[provider] = {
                isHealthy: health.isHealthy,
                consecutiveFailures: health.consecutiveFailures,
                lastFailure: health.lastFailure,
                lastSuccess: health.lastSuccess,
            };
        }
        return status;
    }
    resetProviderHealth(provider) {
        if (this.providerHealth.has(provider)) {
            this.markProviderHealthy(provider);
            this.logger.log(`Manually reset health for provider: ${provider}`);
        }
    }
    resetAllProviderHealth() {
        for (const provider of this.providerHealth.keys()) {
            this.markProviderHealthy(provider);
        }
        this.logger.log('🔧 Circuit breaker reset: All providers marked healthy');
    }
    initializeProviderHealth() {
        const providers = this.llmFactoryService.getAvailableProviders();
        for (const provider of providers) {
            this.providerHealth.set(provider, {
                isHealthy: true,
                lastFailure: null,
                consecutiveFailures: 0,
                lastSuccess: null,
            });
        }
        this.logger.log(`Initialized health tracking for ${providers.length} LLM providers`);
    }
    getHealthyProviders() {
        const allProviders = this.llmFactoryService.getAvailableProviders();
        if (process.env.LLM_HEALTH_CHECK_DISABLE === 'true') {
            this.logger.debug('Health check disabled via LLM_HEALTH_CHECK_DISABLE=true, returning all providers');
            return allProviders;
        }
        const healthyProviders = [];
        const unhealthyProviders = [];
        for (const provider of allProviders) {
            const health = this.providerHealth.get(provider);
            if (health?.isHealthy) {
                healthyProviders.push(provider);
            }
            else {
                unhealthyProviders.push(provider);
                if (health && this.shouldRetryProvider(health)) {
                    healthyProviders.push(provider);
                    this.logger.debug(`Retrying provider ${provider} after cooldown`);
                }
            }
        }
        if (healthyProviders.length === 0) {
            this.logger.warn('No healthy providers available, trying all providers');
            return allProviders;
        }
        return healthyProviders;
    }
    shouldRetryProvider(health) {
        if (!health.lastFailure)
            return true;
        const cooldownMs = Math.min(this.retryDelayMs * Math.pow(2, health.consecutiveFailures), 300000);
        return Date.now() - health.lastFailure.getTime() > cooldownMs;
    }
    markProviderHealthy(provider) {
        const health = this.providerHealth.get(provider);
        if (health) {
            health.isHealthy = true;
            health.consecutiveFailures = 0;
            health.lastSuccess = new Date();
            this.providerHealth.set(provider, health);
        }
    }
    markProviderUnhealthy(provider, error) {
        const health = this.providerHealth.get(provider);
        if (health) {
            health.lastFailure = new Date();
            health.consecutiveFailures += 1;
            const MAX_FAILS = 2;
            if (health.consecutiveFailures >= MAX_FAILS) {
                health.isHealthy = false;
                this.logger.warn(`Provider ${provider} marked unhealthy after ${health.consecutiveFailures} failures`, {
                    consecutiveFailures: health.consecutiveFailures,
                    error: error.message,
                });
            }
            else {
                this.logger.warn(`Provider ${provider} failure ${health.consecutiveFailures}/${MAX_FAILS} (still healthy)`, {
                    consecutiveFailures: health.consecutiveFailures,
                    error: error.message,
                });
            }
            this.providerHealth.set(provider, health);
        }
    }
    async executeWithTimeout(operation, timeoutMs) {
        return Promise.race([
            operation(),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Operation timeout')), timeoutMs)),
        ]);
    }
    getFallbackFollowUpQuestions(context) {
        const questionsByStage = {
            greeting: [
                "What type of work or projects are you working on?",
                "Are you looking for tools for business or personal use?"
            ],
            discovery: [
                "What's your experience level with AI tools?",
                "Do you have a budget in mind?",
                "What's the main challenge you're trying to solve?"
            ],
            refinement: [
                "Would you prefer free tools or are you open to paid options?",
                "How technical do you want the tool to be?"
            ],
            recommendation: [
                "Would you like to see some specific recommendations?",
                "Should I focus on the most popular options?"
            ],
            comparison: [
                "What criteria are most important for your decision?",
                "Would you like me to compare pricing or features?"
            ]
        };
        return questionsByStage[context.conversationStage] || questionsByStage.discovery;
    }
    getFallbackTransitionDecision(context) {
        const messageCount = context.messages.length;
        const entitiesDiscovered = context.discoveredEntities.length;
        const hasPreferences = Object.keys(context.userPreferences).length > 0;
        if (messageCount >= 6 && entitiesDiscovered >= 3 && hasPreferences) {
            return {
                shouldTransition: true,
                reason: 'Sufficient conversation depth and entity discovery for recommendations',
            };
        }
        return {
            shouldTransition: false,
            reason: 'Need more conversation context before transitioning to recommendations',
        };
    }
};
exports.LlmFailoverService = LlmFailoverService;
exports.LlmFailoverService = LlmFailoverService = LlmFailoverService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [llm_factory_service_1.LlmFactoryService])
], LlmFailoverService);
//# sourceMappingURL=llm-failover.service.js.map