{"version": 3, "file": "llm-failover.service.js", "sourceRoot": "", "sources": ["../../../src/chat/services/llm-failover.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,uFAAkF;AAc3E,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAa7B,YAA6B,iBAAoC;QAApC,sBAAiB,GAAjB,iBAAiB,CAAmB;QAZhD,WAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;QAC7C,eAAU,GAAG,CAAC,CAAC;QACf,iBAAY,GAAG,IAAI,CAAC;QAG7B,mBAAc,GAKjB,IAAI,GAAG,EAAE,CAAC;QAGb,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAKD,KAAK,CAAC,2BAA2B,CAC/B,WAAmB,EACnB,OAA4B,EAC5B,iBAAqC;QAErC,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtD,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,QAAQ,EAAE,CAAC,CAAC;gBAGzE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,EAAE;oBACjD,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,IAAI,CAAC;oBACtD,WAAW,EAAE,CAAC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;oBAChE,WAAW,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;oBAClD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;oBAC5C,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC,CAAC;gBAEH,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAC5E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC5C,GAAG,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,EAAE,iBAAiB,CAAC,EACzE,KAAK,CACN,CAAC;gBAGF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,QAAQ,EAAE,EAAE;oBACjD,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;oBACrD,uBAAuB,EAAE,QAAQ,EAAE,kBAAkB,EAAE,MAAM,IAAI,CAAC;oBAClE,qBAAqB,EAAE,CAAC,CAAC,QAAQ,EAAE,kBAAkB,IAAI,QAAQ,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC;oBAC/F,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC,CAAC;gBAGH,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2CAA2C,QAAQ,EAAE,CAAC,CAAC;gBACvE,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,cAAc,QAAQ,OAAO,KAAK,CAAC,IAAI,IAAI,KAAK,EAAE,QAAQ,EAAE,MAAM,IAAI,SAAS,EAAE,EACjF;oBACE,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,YAAY,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;oBAClC,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;oBAClC,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,QAAQ;iBACT,CACF,CAAC;gBAEF,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAG5C,SAAS;YACX,CAAC;QACH,CAAC;QAGD,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IAKD,KAAK,CAAC,0BAA0B,CAC9B,WAAmB,EACnB,OAA4B;QAE5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtD,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,QAAQ,EAAE,CAAC,CAAC;gBAEjF,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAC5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC1C,GAAG,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,EACrD,KAAK,CACN,CAAC;gBAEF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,QAAQ,mCAAmC,EAAE;oBACxE,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC,CAAC;gBAEH,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5C,SAAS;YACX,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QACnF,OAAO;YACL,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE,EAAE;YACZ,UAAU,EAAE,EAAE;YACd,QAAQ,EAAE,EAAE;YACZ,WAAW,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,qCAAqC,CACzC,OAA4B;QAE5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtD,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAC5E,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC7C,GAAG,EAAE,CAAC,UAAU,CAAC,yBAAyB,CAAC,OAAO,CAAC,EACnD,KAAK,CACN,CAAC;gBAEF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,SAAS,CAAC;YACnB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5C,SAAS;YACX,CAAC;QACH,CAAC;QAGD,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAKD,KAAK,CAAC,6CAA6C,CACjD,OAA4B;QAE5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEtD,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAC5E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAC1C,GAAG,EAAE,CAAC,UAAU,CAAC,iCAAiC,CAAC,OAAO,CAAC,EAC3D,KAAK,CACN,CAAC;gBAEF,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5C,SAAS;YACX,CAAC;QACH,CAAC;QAGD,OAAO,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC;IACrD,CAAC;IAKD,uBAAuB;QACrB,MAAM,MAAM,GAAwB,EAAE,CAAC;QAEvC,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,GAAG;gBACjB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;gBAC/C,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,mBAAmB,CAAC,QAAgB;QAClC,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,QAAQ,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAKD,sBAAsB;QACpB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC;YAClD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAC5E,CAAC;IAGO,wBAAwB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;QAEjE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAChC,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI;gBACjB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,SAAS,CAAC,MAAM,gBAAgB,CAAC,CAAC;IACvF,CAAC;IAEO,mBAAmB;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;QACpE,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,MAAM,kBAAkB,GAAa,EAAE,CAAC;QAExC,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAEjD,IAAI,MAAM,EAAE,SAAS,EAAE,CAAC;gBACtB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAGlC,IAAI,MAAM,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC/C,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,QAAQ,iBAAiB,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;QAGD,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;YACzE,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAEO,mBAAmB,CAAC,MAAW;QACrC,IAAI,CAAC,MAAM,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAErC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,EAC3D,MAAM,CACP,CAAC;QAEF,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC;IAChE,CAAC;IAEO,mBAAmB,CAAC,QAAgB;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;YACxB,MAAM,CAAC,mBAAmB,GAAG,CAAC,CAAC;YAC/B,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,QAAgB,EAAE,KAAU;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACjD,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,CAAC,mBAAmB,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,QAAQ,mBAAmB,EAAE;gBACxD,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;gBAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC9B,SAA2B,EAC3B,SAAiB;QAEjB,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,SAAS,EAAE;YACX,IAAI,OAAO,CAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,CAC/B,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,SAAS,CAAC,CACpE;SACF,CAAC,CAAC;IACL,CAAC;IAEO,4BAA4B,CAAC,OAA4B;QAC/D,MAAM,gBAAgB,GAAG;YACvB,QAAQ,EAAE;gBACR,mDAAmD;gBACnD,yDAAyD;aAC1D;YACD,SAAS,EAAE;gBACT,6CAA6C;gBAC7C,+BAA+B;gBAC/B,mDAAmD;aACpD;YACD,UAAU,EAAE;gBACV,8DAA8D;gBAC9D,2CAA2C;aAC5C;YACD,cAAc,EAAE;gBACd,sDAAsD;gBACtD,6CAA6C;aAC9C;YACD,UAAU,EAAE;gBACV,qDAAqD;gBACrD,mDAAmD;aACpD;SACF,CAAC;QAEF,OAAO,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,gBAAgB,CAAC,SAAS,CAAC;IACnF,CAAC;IAEO,6BAA6B,CACnC,OAA4B;QAG5B,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC7C,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC;QAC7D,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAEvE,IAAI,YAAY,IAAI,CAAC,IAAI,kBAAkB,IAAI,CAAC,IAAI,cAAc,EAAE,CAAC;YACnE,OAAO;gBACL,gBAAgB,EAAE,IAAI;gBACtB,MAAM,EAAE,wEAAwE;aACjF,CAAC;QACJ,CAAC;QAED,OAAO;YACL,gBAAgB,EAAE,KAAK;YACvB,MAAM,EAAE,wEAAwE;SACjF,CAAC;IACJ,CAAC;CACF,CAAA;AA3WY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAcqC,uCAAiB;GAbtD,kBAAkB,CA2W9B"}