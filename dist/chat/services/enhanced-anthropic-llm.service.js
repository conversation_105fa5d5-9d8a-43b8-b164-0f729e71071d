"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EnhancedAnthropicLlmService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAnthropicLlmService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let EnhancedAnthropicLlmService = EnhancedAnthropicLlmService_1 = class EnhancedAnthropicLlmService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(EnhancedAnthropicLlmService_1.name);
        this.apiUrl = 'https://api.anthropic.com/v1/messages';
        this.apiKey = this.configService.get('ANTHROPIC_API_KEY');
        if (!this.apiKey) {
            this.logger.warn('ANTHROPIC_API_KEY is not set in environment variables.');
        }
    }
    async getRecommendation(problemDescription, candidateEntities) {
        this.logger.log(`Getting enhanced Anthropic recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`);
        if (!this.apiKey) {
            this.logger.warn('Anthropic API key not available, using enhanced fallback');
            return this.getEnhancedFallbackRecommendation(candidateEntities);
        }
        try {
            const prompt = this.buildEnhancedRecommendationPrompt(problemDescription, candidateEntities);
            const response = await this.callAnthropicAPI(prompt);
            const recommendation = this.parseAnthropicResponse(response, candidateEntities);
            this.logger.log(`Enhanced Anthropic recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`);
            return recommendation;
        }
        catch (error) {
            this.logger.error('Error generating enhanced Anthropic recommendation', error.stack);
            return this.getEnhancedFallbackRecommendation(candidateEntities);
        }
    }
    async getChatResponse(userMessage, context, candidateEntities) {
        const startTime = Date.now();
        this.logger.log(`Getting enhanced Anthropic chat response for session: ${context.sessionId}, stage: ${context.conversationStage}`);
        if (!this.apiKey) {
            this.logger.warn('Anthropic API key not available, using enhanced fallback');
            return this.getEnhancedFallbackChatResponse(userMessage, context);
        }
        try {
            const processedContext = this.preprocessConversationContext(context);
            const repetitionAnalysis = this.analyzeRepetition(userMessage, processedContext);
            const intent = await this.classifyIntent(userMessage, processedContext);
            const prompt = this.buildEnhancedChatPrompt(userMessage, processedContext, intent, candidateEntities, repetitionAnalysis);
            const response = await this.callAnthropicAPI(prompt);
            const chatResponse = this.parseEnhancedChatResponse(response, intent, processedContext, candidateEntities, repetitionAnalysis);
            const validatedResponse = this.validateAndEnhanceResponse(chatResponse, userMessage, processedContext, repetitionAnalysis);
            const finalValidatedResponse = validatedResponse;
            finalValidatedResponse.metadata = {
                responseTime: Date.now() - startTime,
                llmProvider: 'ANTHROPIC_ENHANCED',
                tokensUsed: response.length,
            };
            return finalValidatedResponse;
        }
        catch (error) {
            this.logger.error('Error generating enhanced Anthropic chat response', error.stack);
            return this.getEnhancedFallbackChatResponse(userMessage, context);
        }
    }
    async classifyIntent(userMessage, context) {
        if (!this.apiKey)
            return this.getEnhancedFallbackIntent(userMessage, context);
        try {
            const prompt = this.buildEnhancedIntentClassificationPrompt(userMessage, context);
            const response = await this.callAnthropicAPI(prompt);
            return this.parseIntentResponse(response);
        }
        catch (error) {
            this.logger.error('Error classifying intent', error.stack);
            return this.getEnhancedFallbackIntent(userMessage, context);
        }
    }
    async generateFollowUpQuestions(context) {
        if (!this.apiKey)
            return this.getEnhancedFallbackFollowUpQuestions(context);
        try {
            const prompt = this.buildEnhancedFollowUpPrompt(context);
            const response = await this.callAnthropicAPI(prompt);
            return this.parseFollowUpResponse(response);
        }
        catch (error) {
            this.logger.error('Error generating follow-up questions', error.stack);
            return this.getEnhancedFallbackFollowUpQuestions(context);
        }
    }
    async shouldTransitionToRecommendations(context) {
        if (!this.apiKey)
            return this.getEnhancedTransitionDecision(context);
        try {
            const prompt = this.buildEnhancedTransitionPrompt(context);
            const response = await this.callAnthropicAPI(prompt);
            return this.parseTransitionResponse(response);
        }
        catch (error) {
            this.logger.error('Error evaluating transition', error.stack);
            return this.getEnhancedTransitionDecision(context);
        }
    }
    preprocessConversationContext(context) {
        const processedContext = { ...context };
        processedContext.metadata = {
            ...processedContext.metadata,
            conversationLength: context.messages?.length || 0,
            userMessageCount: context.messages?.filter(m => m.role === 'user').length || 0,
            assistantMessageCount: context.messages?.filter(m => m.role === 'assistant').length || 0,
            lastUserMessage: context.messages?.filter(m => m.role === 'user').pop()?.content || '',
            conversationTopics: this.extractConversationTopics(context),
            userEngagementLevel: this.assessUserEngagement(context),
        };
        return processedContext;
    }
    analyzeRepetition(userMessage, context) {
        const userMessages = context.messages?.filter(m => m.role === 'user') || [];
        const currentMessageLower = userMessage.toLowerCase().trim();
        const exactMatches = userMessages.filter(msg => msg.content.toLowerCase().trim() === currentMessageLower);
        const similarQuestions = userMessages.filter(msg => {
            const msgLower = msg.content.toLowerCase();
            const similarity = this.calculateStringSimilarity(currentMessageLower, msgLower);
            return similarity > 0.7 && similarity < 1.0;
        }).map(msg => msg.content);
        return {
            isRepeated: exactMatches.length > 0,
            previousOccurrences: exactMatches.length,
            lastOccurrence: exactMatches.length > 0 ? exactMatches[exactMatches.length - 1].content : null,
            similarQuestions,
            repetitionType: exactMatches.length > 0 ? 'exact' : (similarQuestions.length > 0 ? 'similar' : 'none'),
        };
    }
    calculateStringSimilarity(str1, str2) {
        const set1 = new Set(str1.toLowerCase().split(/\s+/));
        const set2 = new Set(str2.toLowerCase().split(/\s+/));
        const intersection = new Set([...set1].filter(x => set2.has(x)));
        const union = new Set([...set1, ...set2]);
        return intersection.size / union.size;
    }
    extractConversationTopics(context) {
        const topics = new Set();
        const messages = context.messages || [];
        const topicKeywords = {
            'work': ['work', 'job', 'business', 'company', 'professional'],
            'education': ['education', 'learning', 'student', 'teaching'],
            'content': ['content', 'writing', 'video', 'image', 'creative'],
            'automation': ['automation', 'workflow', 'process', 'efficiency'],
            'data': ['data', 'analysis', 'analytics', 'insights'],
            'programming': ['code', 'coding', 'programming', 'development'],
        };
        messages.forEach(msg => {
            const content = msg.content.toLowerCase();
            Object.entries(topicKeywords).forEach(([topic, keywords]) => {
                if (keywords.some(keyword => content.includes(keyword))) {
                    topics.add(topic);
                }
            });
        });
        return Array.from(topics);
    }
    assessUserEngagement(context) {
        const messageCount = context.messages?.length || 0;
        const userMessages = context.messages?.filter(m => m.role === 'user') || [];
        const avgMessageLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / Math.max(userMessages.length, 1);
        if (messageCount >= 6 && avgMessageLength > 50)
            return 'high';
        if (messageCount >= 3 && avgMessageLength > 20)
            return 'medium';
        return 'low';
    }
    buildEnhancedChatPrompt(userMessage, context, intent, candidateEntities, repetitionAnalysis) {
        const conversationHistory = context.messages?.slice(-8).map(msg => `${msg.role}: ${msg.content}`).join('\n') || '';
        const entitiesContext = candidateEntities ? this.formatEntitiesForChat(candidateEntities) : '';
        const userProfile = this.formatUserProfile(context);
        let repetitionContext = '';
        if (repetitionAnalysis?.isRepeated) {
            repetitionContext = `
**🚨 CRITICAL: REPETITION DETECTED**
- User has asked this EXACT question ${repetitionAnalysis.previousOccurrences} time(s) before
- Previous identical question: "${repetitionAnalysis.lastOccurrence}"
- You MUST acknowledge this repetition and provide a DIFFERENT perspective or ask for clarification
- DO NOT give the same response as before
- Be helpful but acknowledge the repetition directly`;
        }
        else if (repetitionAnalysis?.similarQuestions.length > 0) {
            repetitionContext = `
**⚠️ SIMILAR QUESTIONS DETECTED**
- User has asked similar questions before: ${repetitionAnalysis.similarQuestions.slice(0, 2).join(', ')}
- Provide a fresh perspective or build upon previous discussions
- Avoid repeating the same information`;
        }
        return `You are the world's best AI assistant for discovering and recommending AI tools. You are conversational, knowledgeable, and focused on helping users find the perfect AI solutions for their specific needs.

${repetitionContext}

**ABSOLUTE RULES - NEVER VIOLATE THESE:**
1. NEVER say "I apologize, but I'm having trouble accessing our conversation history"
2. NEVER claim you can't access conversation history - you can see it below
3. ALWAYS acknowledge what the user has already told you
4. NEVER ask questions you've already asked in this conversation
5. If user repeats a question, acknowledge it and provide a NEW angle or ask for clarification
6. Build upon previous knowledge rather than starting over
7. Be engaging, helpful, and move the conversation forward
8. 🎯 CRITICAL: ONLY mention AI tools that are in the "Relevant AI Tools Available" list below
9. 🎯 NEVER invent or hallucinate tool names, IDs, or details not in the provided list
10. 🚀 RECOMMEND-FIRST RULE: If you received ANY tool candidates, you MUST recommend 1-3 of them
11. 🚀 Only ask clarifying questions when the candidate list is EMPTY
12. 🎯 If no relevant tools are provided, focus on understanding user needs better

**Current Conversation Context:**
- Session: ${context.sessionId}
- Stage: ${context.conversationStage}
- Messages: ${context.messages?.length || 0}
- User Intent: ${intent.type} (confidence: ${intent.confidence})
- Topics Discussed: ${context.metadata?.conversationTopics?.join(', ') || 'None yet'}

**User Profile:**
${userProfile}

**Full Conversation History:**
${conversationHistory}

**Current User Message:**
"${userMessage}"

${entitiesContext ? `**Relevant AI Tools Available:**\n${entitiesContext}` : ''}

**Your Task:**
1. Respond naturally and helpfully to the user's message
2. Use the conversation history to provide context-aware responses
3. If entities are provided, mention relevant ones naturally using their EXACT names and IDs
4. Ask NEW questions that haven't been covered before (if needed)
5. Guide the user toward finding the perfect AI tools for their needs
6. Be encouraging and build rapport

**Response Format (JSON):**
{
  "message": "Your engaging, context-aware response here",
  "discoveredEntities": [{"id": "EXACT-ID-FROM-LIST", "name": "EXACT-NAME-FROM-LIST", "relevanceScore": 0.9, "reason": "Why it's relevant"}],
  "followUpQuestions": ["Only NEW questions that build on the conversation"],
  "suggestedActions": [{"type": "explore_tool", "label": "Learn more about...", "data": {}}],
  "shouldTransitionToRecommendations": false,
  "conversationStage": "${context.conversationStage}"
}

**CRITICAL FOR discoveredEntities:**
- ONLY use entity IDs and names from the "Relevant AI Tools Available" list above
- If no relevant tools are provided, use an empty array: "discoveredEntities": []
- NEVER invent tool names or IDs that aren't in the provided list
- Use EXACT entity IDs and names as shown in the list

Remember: You are building the world's best AI tool recommendation system. Be helpful, engaging, and always move the conversation forward!`;
    }
    buildEnhancedRecommendationPrompt(problemDescription, candidateEntities) {
        const entitiesContext = candidateEntities
            .map((entity, index) => {
            const categories = entity.categories?.map((c) => c.category.name).join(', ') || 'General';
            const tags = entity.tags?.map((t) => t.tag.name).join(', ') || 'None';
            const features = entity.features?.map((f) => f.feature.name).join(', ') || 'Various';
            return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType?.name || 'AI Tool'}
   - Description: ${entity.shortDescription || entity.description || 'AI tool for various tasks'}
   - Categories: ${categories}
   - Tags: ${tags}
   - Features: ${features}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'Not yet rated'}
   - Pricing: See website for pricing`;
        })
            .join('\n\n');
        return `You are the world's leading expert in AI tools and technology. Your mission is to provide the most accurate, helpful, and insightful recommendations.

**User's Need:**
"${problemDescription}"

**Available AI Tools:**
${entitiesContext}

**Your Task:**
Analyze the user's specific needs and recommend the TOP 3-5 most suitable AI tools from the list above.

**Evaluation Criteria:**
1. Direct relevance to the user's problem
2. Tool capabilities and features
3. User ratings and reviews
4. Ease of use and learning curve
5. Value for money
6. Integration capabilities
7. Community and support

**Response Format (JSON):**
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [specific problem], I recommend these tools: 1) [Tool Name] - [specific reason why it's perfect for their need]... 2) [Tool Name] - [specific reason]... 3) [Tool Name] - [specific reason]... Each recommendation should be tailored to their exact requirements."
}

**Important:** Only include entity IDs from the provided list. Focus on quality over quantity - better to recommend 3 perfect matches than 5 mediocre ones.`;
    }
    buildEnhancedIntentClassificationPrompt(userMessage, context) {
        const recentMessages = context.messages?.slice(-3).map(msg => `${msg.role}: ${msg.content}`).join('\n') || '';
        return `Analyze the user's intent from their message and conversation context.

**Conversation Context:**
${recentMessages}

**Current User Message:**
"${userMessage}"

**Intent Categories:**
- discovery: User is exploring what AI tools exist for their needs
- comparison: User wants to compare specific tools or categories  
- specific_tool: User is asking about a particular tool
- general_question: User has general questions about AI tools
- refinement: User is narrowing down their requirements
- recommendation_request: User is ready for specific recommendations

**Response Format (JSON):**
{
  "type": "discovery|comparison|specific_tool|general_question|refinement|recommendation_request",
  "confidence": 0.85,
  "entities": ["mentioned entity names"],
  "categories": ["mentioned categories"],
  "features": ["mentioned features"],
  "constraints": {
    "budget": "free|low|medium|high|not_specified",
    "technical_level": "beginner|intermediate|advanced|not_specified",
    "use_case": "specific use case if mentioned"
  }
}`;
    }
    buildEnhancedFollowUpPrompt(context) {
        const lastMessage = context.messages?.[context.messages.length - 1];
        const previousQuestions = this.extractPreviousQuestions(context);
        return `Generate 2-3 intelligent follow-up questions to help the user discover the right AI tools.

**Conversation Stage:** ${context.conversationStage}
**User's Last Message:** "${lastMessage?.content || 'No previous message'}"
**Discovered Entities:** ${context.discoveredEntities?.length || 0} tools found so far
**Topics Discussed:** ${context.metadata?.conversationTopics?.join(', ') || 'None'}

**Questions Already Asked:**
${previousQuestions.length > 0 ? previousQuestions.join('\n- ') : 'None yet'}

**Guidelines:**
- Ask questions that help narrow down their specific needs
- Consider their technical level and use case
- Be conversational and helpful
- Focus on practical aspects like budget, features, or use cases
- NEVER repeat questions that have already been asked
- Build upon information already gathered

**Response Format (JSON):**
{
  "questions": ["New question 1?", "New question 2?", "New question 3?"]
}`;
    }
    buildEnhancedTransitionPrompt(context) {
        return `Determine if the conversation is ready to transition to formal recommendations.

**Conversation Analysis:**
- Stage: ${context.conversationStage}
- Messages: ${context.messages?.length || 0}
- Discovered Entities: ${context.discoveredEntities?.length || 0}
- User Preferences: ${JSON.stringify(context.userPreferences)}
- Topics Covered: ${context.metadata?.conversationTopics?.join(', ') || 'None'}
- Engagement Level: ${context.metadata?.userEngagementLevel || 'unknown'}

**Transition Criteria:**
- User has clearly expressed their needs
- We have enough information about their requirements
- User seems ready for specific recommendations
- Conversation has progressed beyond initial discovery
- User has engaged meaningfully (not just one-word answers)

**Response Format (JSON):**
{
  "shouldTransition": true/false,
  "reason": "Detailed explanation of the decision"
}`;
    }
    async callAnthropicAPI(prompt) {
        const requestBody = {
            model: 'claude-3-haiku-20240307',
            max_tokens: 2000,
            temperature: 0.7,
            messages: [
                {
                    role: 'user',
                    content: prompt,
                },
            ],
        };
        const headers = {
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
        };
        if (this.apiKey) {
            headers['x-api-key'] = this.apiKey;
        }
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers,
            body: JSON.stringify(requestBody),
        });
        if (!response.ok) {
            throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (data.content && data.content[0]?.text) {
            return data.content[0].text;
        }
        throw new Error('Invalid response format from Anthropic API');
    }
    parseEnhancedChatResponse(response, intent, context, candidateEntities, repetitionAnalysis) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                throw new Error('No JSON found in response');
            const parsed = JSON.parse(jsonMatch[0]);
            this.logger.debug(`🔍 PARSING DEBUG - Original discoveredEntities: ${JSON.stringify(parsed.discoveredEntities)}`);
            this.logger.debug(`🔍 PARSING DEBUG - Candidate entities count: ${candidateEntities?.length || 0}`);
            const validatedDiscoveredEntities = this.validateDiscoveredEntities(parsed.discoveredEntities || [], candidateEntities || []);
            this.logger.debug(`🔍 PARSING DEBUG - Validated discoveredEntities: ${JSON.stringify(validatedDiscoveredEntities)}`);
            return {
                message: parsed.message || this.getContextAwareFallbackMessage(context, repetitionAnalysis),
                intent,
                discoveredEntities: validatedDiscoveredEntities,
                followUpQuestions: parsed.followUpQuestions || [],
                suggestedActions: parsed.suggestedActions || [],
                shouldTransitionToRecommendations: parsed.shouldTransitionToRecommendations || false,
                conversationStage: parsed.conversationStage || context.conversationStage,
                metadata: { responseTime: 0, llmProvider: 'ANTHROPIC_ENHANCED' },
            };
        }
        catch (error) {
            this.logger.warn('Failed to parse enhanced chat response, using enhanced fallback', error.message);
            return this.getEnhancedFallbackChatResponse('', context);
        }
    }
    validateDiscoveredEntities(discoveredEntities, candidateEntities) {
        try {
            if (!discoveredEntities || !Array.isArray(discoveredEntities)) {
                this.logger.debug('No discovered entities to validate');
                return [];
            }
            if (!candidateEntities || candidateEntities.length === 0) {
                this.logger.warn('No candidate entities provided for validation - this may indicate an issue with entity discovery');
                return [];
            }
            const validEntityIds = new Set(candidateEntities.map(e => e.id));
            const validEntityNames = new Map(candidateEntities.map(e => [e.name.toLowerCase(), e]));
            const validatedEntities = [];
            for (const entity of discoveredEntities) {
                let validEntity = null;
                if (entity.id && validEntityIds.has(entity.id)) {
                    validEntity = candidateEntities.find(e => e.id === entity.id) || null;
                }
                if (!validEntity && entity.name) {
                    const nameLower = entity.name.toLowerCase();
                    validEntity = validEntityNames.get(nameLower) || null;
                }
                if (validEntity) {
                    validatedEntities.push({
                        id: validEntity.id,
                        name: validEntity.name,
                        relevanceScore: Math.min(Math.max(entity.relevanceScore || 0.8, 0), 1),
                        reason: entity.reason || `Relevant AI tool for your needs`
                    });
                    this.logger.debug(`Validated entity: ${validEntity.name} (${validEntity.id})`);
                }
                else {
                    this.logger.warn(`🚨 HALLUCINATED ENTITY DETECTED: ${JSON.stringify(entity)} - This entity does not exist in our database!`);
                }
            }
            this.logger.log(`Validated ${validatedEntities.length} out of ${discoveredEntities.length} discovered entities`);
            return validatedEntities;
        }
        catch (error) {
            this.logger.error('Entity validation failed completely, using fallback', error.message);
            return [];
        }
    }
    validateMessageContentForHallucination(response, candidateEntities) {
        if (!response.message) {
            return response;
        }
        const validToolNames = new Set(candidateEntities.map(entity => entity.name.toLowerCase()));
        const suspiciousPatterns = [
            /\b(ChatGPT|Claude|GPT-4|GPT-3|Midjourney|DALL-E|DALL·E|Stable Diffusion|Runway ML|Runway|Synthesia|Jasper|Copy\.ai|Writesonic|Grammarly|Notion AI|Canva|Figma|Adobe Firefly|Filmora|DaVinci Resolve|Luma|Pika|Descript|Replicate|Hugging Face|OpenAI|Anthropic|Cohere|AI21|Perplexity|Character\.ai|Bard|Bing Chat|GitHub Copilot|Codex|InstructGPT|PaLM|LaMDA|BERT|T5|RoBERTa)\b/gi,
            /\b([A-Z][a-z]{2,}(?:[A-Z][a-z]+)*)\s+(AI|Pro|Plus|Studio|Bot|Assistant|Generator|Creator|Maker|Builder)\b/g,
            /\b(Google|Microsoft|Meta|Facebook|Amazon|Apple|IBM|NVIDIA|Intel|Salesforce|Oracle)\s+(AI|Bard|Copilot|Assistant|Bot)\b/gi,
        ];
        let messageText = response.message;
        let foundHallucinations = false;
        for (const pattern of suspiciousPatterns) {
            const matches = messageText.match(pattern);
            if (matches) {
                for (const match of matches) {
                    const toolName = match.toLowerCase().trim();
                    const commonWords = ['ai', 'pro', 'plus', 'studio', 'the', 'and', 'or', 'for', 'with', 'to', 'in', 'on', 'at', 'by'];
                    if (commonWords.includes(toolName) || toolName.length < 3) {
                        continue;
                    }
                    if (!validToolNames.has(toolName) && !this.isGenericTerm(toolName)) {
                        this.logger.warn(`🚨 HALLUCINATED TOOL IN MESSAGE: "${match}" - This tool is not in our database!`);
                        foundHallucinations = true;
                        const genericReplacement = this.getGenericReplacement(match);
                        messageText = messageText.replace(new RegExp(match, 'gi'), genericReplacement);
                    }
                }
            }
        }
        if (foundHallucinations) {
            this.logger.warn(`🎯 MESSAGE CONTENT SANITIZED: Removed hallucinated tool mentions`);
            messageText += '\n\n*Note: I can only recommend AI tools that are verified and available in our database.*';
            return {
                ...response,
                message: messageText,
            };
        }
        return response;
    }
    isGenericTerm(term) {
        const genericTerms = [
            'ai', 'tool', 'software', 'platform', 'service', 'app', 'application',
            'system', 'solution', 'technology', 'program', 'website', 'online',
            'digital', 'virtual', 'smart', 'intelligent', 'automated', 'machine',
            'learning', 'neural', 'network', 'algorithm', 'model', 'api'
        ];
        return genericTerms.includes(term.toLowerCase());
    }
    getGenericReplacement(toolName) {
        if (toolName.toLowerCase().includes('ai')) {
            return 'AI tools';
        }
        if (toolName.toLowerCase().includes('video') || toolName.toLowerCase().includes('edit')) {
            return 'video editing tools';
        }
        if (toolName.toLowerCase().includes('write') || toolName.toLowerCase().includes('text')) {
            return 'writing tools';
        }
        if (toolName.toLowerCase().includes('image') || toolName.toLowerCase().includes('photo')) {
            return 'image generation tools';
        }
        return 'AI tools';
    }
    validateAndEnhanceResponse(response, userMessage, context, repetitionAnalysis) {
        const problematicPhrases = [
            'i apologize, but i\'m having trouble accessing',
            'i don\'t have access to our conversation history',
            'let\'s start fresh',
            'i can\'t see our previous conversation'
        ];
        const messageLower = response.message.toLowerCase();
        const hasProblematicPhrase = problematicPhrases.some(phrase => messageLower.includes(phrase));
        if (hasProblematicPhrase) {
            this.logger.warn('Detected problematic response, replacing with enhanced fallback');
            response.message = this.getContextAwareFallbackMessage(context, repetitionAnalysis);
        }
        if (response.followUpQuestions) {
            const previousQuestions = this.extractPreviousQuestions(context);
            response.followUpQuestions = response.followUpQuestions.filter(q => !previousQuestions.some(prev => this.calculateStringSimilarity(q.toLowerCase(), prev.toLowerCase()) > 0.8));
        }
        return response;
    }
    getContextAwareFallbackMessage(context, repetitionAnalysis) {
        const messageCount = context.messages?.length || 0;
        const stage = context.conversationStage;
        const topics = context.metadata?.conversationTopics || [];
        if (repetitionAnalysis?.isRepeated) {
            return `I notice you've asked this question before. Let me approach it from a different angle - could you tell me more about what specific aspect you'd like me to focus on? For example, are you looking for budget-friendly options, enterprise solutions, or tools with specific features?`;
        }
        if (messageCount === 0) {
            return `Hello! I'm here to help you discover the perfect AI tools for your needs. I specialize in understanding exactly what you're trying to accomplish and matching you with the best AI solutions available. What kind of project or challenge are you working on?`;
        }
        if (topics.length > 0) {
            const topicList = topics.slice(0, 2).join(' and ');
            return `Based on our discussion about ${topicList}, I can help you find some excellent AI tools. What specific features or capabilities are most important to you right now?`;
        }
        const stageMessages = {
            greeting: `Great to meet you! I'm excited to help you find the perfect AI tools. What kind of work or projects are you focusing on?`,
            discovery: `I'm getting a good sense of what you need. Let me help you explore some specific options that could work well for your situation.`,
            refinement: `Perfect! Now that I understand your requirements better, I can point you toward some excellent solutions.`,
            recommendation: `Based on everything we've discussed, I have some fantastic AI tools in mind that should be perfect for your needs.`,
            comparison: `Let me help you compare the best options so you can make the right choice for your specific situation.`
        };
        return stageMessages[stage] || stageMessages.discovery;
    }
    extractPreviousQuestions(context) {
        const questions = [];
        const messages = context.messages || [];
        messages.forEach(msg => {
            if (msg.role === 'assistant' && msg.content.includes('?')) {
                const questionMatches = msg.content.match(/[^.!]*\?/g);
                if (questionMatches) {
                    questions.push(...questionMatches.map(q => q.trim()));
                }
            }
        });
        return questions;
    }
    parseAnthropicResponse(response, candidateEntities) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                throw new Error('No JSON found in response');
            const parsed = JSON.parse(jsonMatch[0]);
            if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
                throw new Error('Invalid response structure');
            }
            const validEntityIds = candidateEntities.map((e) => e.id);
            const filteredIds = parsed.recommendedEntityIds.filter((id) => validEntityIds.includes(id));
            return {
                recommendedEntityIds: filteredIds.slice(0, 5),
                explanation: parsed.explanation || 'AI recommendation generated successfully.',
            };
        }
        catch (error) {
            this.logger.warn('Failed to parse Anthropic response, using enhanced fallback', error.message);
            return this.getEnhancedFallbackRecommendation(candidateEntities);
        }
    }
    parseIntentResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                throw new Error('No JSON found in response');
            const parsed = JSON.parse(jsonMatch[0]);
            return {
                type: parsed.type || 'discovery',
                confidence: parsed.confidence || 0.5,
                entities: parsed.entities || [],
                categories: parsed.categories || [],
                features: parsed.features || [],
                constraints: parsed.constraints || {},
            };
        }
        catch (error) {
            this.logger.warn('Failed to parse intent response, using enhanced fallback', error.message);
            return this.getEnhancedFallbackIntent('', {});
        }
    }
    parseFollowUpResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                throw new Error('No JSON found in response');
            const parsed = JSON.parse(jsonMatch[0]);
            return parsed.questions || [];
        }
        catch (error) {
            this.logger.warn('Failed to parse follow-up response, using enhanced fallback', error.message);
            return [];
        }
    }
    parseTransitionResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch)
                throw new Error('No JSON found in response');
            const parsed = JSON.parse(jsonMatch[0]);
            return {
                shouldTransition: parsed.shouldTransition || false,
                reason: parsed.reason || 'Automatic evaluation',
            };
        }
        catch (error) {
            this.logger.warn('Failed to parse transition response, using enhanced fallback', error.message);
            return { shouldTransition: false, reason: 'Error in evaluation' };
        }
    }
    formatEntitiesForChat(entities) {
        return entities.slice(0, 5).map((entity, index) => {
            const categories = entity.categories?.map(c => c.category.name).join(', ') || 'General';
            const features = entity.features?.map(f => f.feature.name).join(', ') || 'Various features';
            return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - ${entity.shortDescription || entity.description || 'AI tool'}
   - Categories: ${categories}
   - Key Features: ${features}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5` : 'Not rated'}
   - 🎯 Use EXACT ID: ${entity.id}`;
        }).join('\n\n');
    }
    formatUserProfile(context) {
        const prefs = context.userPreferences || {};
        return `- Technical Level: ${prefs.technical_level || 'Not specified'}
- Budget Preference: ${prefs.budget || 'Not specified'}
- Preferred Categories: ${prefs.preferred_categories?.join(', ') || 'None specified'}
- Conversation Stage: ${context.conversationStage}
- Tools Discovered: ${context.discoveredEntities?.length || 0}
- Engagement Level: ${context.metadata?.userEngagementLevel || 'Not assessed'}`;
    }
    getEnhancedFallbackRecommendation(candidateEntities) {
        const sortedEntities = candidateEntities
            .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
            .slice(0, 3);
        return {
            recommendedEntityIds: sortedEntities.map((e) => e.id),
            explanation: `Based on the available options and user ratings, I've selected the top-performing AI tools that should help with your needs. Each of these tools has proven effectiveness and positive user feedback.`,
        };
    }
    getEnhancedFallbackChatResponse(userMessage, context) {
        const message = this.getContextAwareFallbackMessage(context);
        return {
            message,
            intent: this.getEnhancedFallbackIntent(userMessage, context),
            followUpQuestions: this.getEnhancedFallbackFollowUpQuestions(context),
            shouldTransitionToRecommendations: false,
            conversationStage: context.conversationStage,
            metadata: { responseTime: 0, llmProvider: 'ANTHROPIC_ENHANCED_FALLBACK' },
        };
    }
    getEnhancedFallbackIntent(userMessage, context) {
        const messageLower = userMessage.toLowerCase();
        if (messageLower.includes('recommend') || messageLower.includes('suggest')) {
            return { type: 'refinement', confidence: 0.7, entities: [], categories: [], features: [], constraints: {} };
        }
        if (messageLower.includes('compare') || messageLower.includes('vs') || messageLower.includes('versus')) {
            return { type: 'comparison', confidence: 0.7, entities: [], categories: [], features: [], constraints: {} };
        }
        if (messageLower.includes('what is') || messageLower.includes('tell me about')) {
            return { type: 'specific_tool', confidence: 0.6, entities: [], categories: [], features: [], constraints: {} };
        }
        return { type: 'discovery', confidence: 0.5, entities: [], categories: [], features: [], constraints: {} };
    }
    getEnhancedFallbackFollowUpQuestions(context) {
        const stage = context.conversationStage;
        const messageCount = context.messages?.length || 0;
        const questionsByStage = {
            greeting: [
                "What type of work or projects are you focusing on?",
                "Are you looking for AI tools for business or personal use?"
            ],
            discovery: [
                "What's your experience level with AI tools?",
                "Do you have a specific budget range in mind?",
                "What's the main challenge you're trying to solve?"
            ],
            refinement: [
                "Would you prefer free tools or are you open to paid solutions?",
                "How important is ease of use versus advanced features?",
                "Do you need tools that integrate with other software?"
            ],
            recommendation: [
                "Would you like to see some specific recommendations based on our discussion?",
                "Should I focus on the most popular options or newer innovative tools?"
            ],
            comparison: [
                "What criteria are most important for your decision?",
                "Would you like me to compare pricing or features?"
            ]
        };
        return questionsByStage[stage] || questionsByStage.discovery;
    }
    getEnhancedTransitionDecision(context) {
        const messageCount = context.messages?.length || 0;
        const entitiesDiscovered = context.discoveredEntities?.length || 0;
        const hasPreferences = Object.keys(context.userPreferences || {}).length > 0;
        const topics = context.metadata?.conversationTopics || [];
        if (messageCount >= 6 && entitiesDiscovered >= 3 && hasPreferences && topics.length >= 2) {
            return {
                shouldTransition: true,
                reason: 'User has engaged meaningfully with sufficient context and entity discovery for quality recommendations',
            };
        }
        if (messageCount >= 8) {
            return {
                shouldTransition: true,
                reason: 'Conversation has sufficient depth to provide recommendations',
            };
        }
        return {
            shouldTransition: false,
            reason: 'Need more conversation context and user engagement before transitioning to recommendations',
        };
    }
};
exports.EnhancedAnthropicLlmService = EnhancedAnthropicLlmService;
exports.EnhancedAnthropicLlmService = EnhancedAnthropicLlmService = EnhancedAnthropicLlmService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EnhancedAnthropicLlmService);
//# sourceMappingURL=enhanced-anthropic-llm.service.js.map