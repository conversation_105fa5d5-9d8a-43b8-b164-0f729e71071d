"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ChatCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatCacheService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const lru_cache_1 = require("lru-cache");
let ChatCacheService = ChatCacheService_1 = class ChatCacheService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ChatCacheService_1.name);
        this.config = {
            intentCacheTtl: this.configService.get('CHAT_INTENT_CACHE_TTL_MS', 180000),
            followUpCacheTtl: this.configService.get('CHAT_FOLLOWUP_CACHE_TTL_MS', 120000),
            entitySearchCacheTtl: this.configService.get('CHAT_ENTITY_CACHE_TTL_MS', 900000),
            responseCacheTtl: this.configService.get('CHAT_RESPONSE_CACHE_TTL_MS', 30000),
            maxCacheSize: this.configService.get('CHAT_CACHE_MAX_SIZE', 1000),
        };
        this.intentCache = new lru_cache_1.LRUCache({
            max: this.config.maxCacheSize,
            ttl: this.config.intentCacheTtl,
            updateAgeOnGet: true,
        });
        this.followUpCache = new lru_cache_1.LRUCache({
            max: this.config.maxCacheSize,
            ttl: this.config.followUpCacheTtl,
            updateAgeOnGet: true,
        });
        this.entitySearchCache = new lru_cache_1.LRUCache({
            max: this.config.maxCacheSize,
            ttl: this.config.entitySearchCacheTtl,
            updateAgeOnGet: true,
        });
        this.responseCache = new lru_cache_1.LRUCache({
            max: this.config.maxCacheSize / 2,
            ttl: this.config.responseCacheTtl,
            updateAgeOnGet: false,
        });
        this.logger.log('Chat cache service initialized with LRU caches');
    }
    async getOrSetIntent(key, computeFn) {
        const cached = this.intentCache.get(key);
        if (cached) {
            this.logger.debug(`Intent cache hit for key: ${key}`);
            return cached;
        }
        this.logger.debug(`Intent cache miss for key: ${key}`);
        const result = await computeFn();
        this.intentCache.set(key, result);
        return result;
    }
    async getOrSetFollowUpQuestions(key, computeFn) {
        const cached = this.followUpCache.get(key);
        if (cached) {
            this.logger.debug(`Follow-up cache hit for key: ${key}`);
            return cached;
        }
        this.logger.debug(`Follow-up cache miss for key: ${key}`);
        const result = await computeFn();
        this.followUpCache.set(key, result);
        return result;
    }
    async getOrSetEntitySearch(query, computeFn) {
        const key = this.normalizeSearchQuery(query);
        const cached = this.entitySearchCache.get(key);
        if (cached) {
            this.logger.debug(`Entity search cache hit for query: ${query}`);
            return cached;
        }
        this.logger.debug(`Entity search cache miss for query: ${query}`);
        const result = await computeFn();
        this.entitySearchCache.set(key, result);
        return result;
    }
    async getOrSetChatResponse(key, computeFn) {
        const cached = this.responseCache.get(key);
        if (cached) {
            this.logger.debug(`Response cache hit for key: ${key}`);
            return {
                ...cached,
                metadata: {
                    ...cached.metadata,
                    responseTime: 0,
                },
            };
        }
        this.logger.debug(`Response cache miss for key: ${key}`);
        const result = await computeFn();
        this.responseCache.set(key, result);
        return result;
    }
    generateIntentKey(message, contextHash) {
        return `intent:${this.hashString(message)}:${contextHash}`;
    }
    generateFollowUpKey(conversationStage, entitiesCount, preferencesHash) {
        return `followup:${conversationStage}:${entitiesCount}:${preferencesHash}`;
    }
    generateResponseKey(message, contextHash, entitiesHash, sessionId) {
        const sessionPrefix = sessionId ? `${sessionId}:` : '';
        return `response:${sessionPrefix}${this.hashString(message)}:${contextHash}:${entitiesHash}`;
    }
    generateContextHash(context) {
        const relevantContext = {
            stage: context.conversationStage,
            preferences: context.userPreferences,
            entitiesCount: context.discoveredEntities?.length || 0,
            messageCount: context.messages?.length || 0,
            recentMessages: context.messages?.slice(-3).map((m) => ({
                role: m.role,
                contentHash: this.hashString(m.content)
            })) || [],
            discussedTopics: this.extractDiscussedTopicsFromContext(context),
        };
        return this.hashString(JSON.stringify(relevantContext));
    }
    generateEntitiesHash(entities) {
        const entityIds = entities.map(e => e.id).sort();
        return this.hashString(JSON.stringify(entityIds));
    }
    getCacheStats() {
        return {
            intent: {
                size: this.intentCache.size,
                maxSize: this.intentCache.max,
                hitRate: this.calculateHitRate(this.intentCache),
            },
            followUp: {
                size: this.followUpCache.size,
                maxSize: this.followUpCache.max,
                hitRate: this.calculateHitRate(this.followUpCache),
            },
            entitySearch: {
                size: this.entitySearchCache.size,
                maxSize: this.entitySearchCache.max,
                hitRate: this.calculateHitRate(this.entitySearchCache),
            },
            response: {
                size: this.responseCache.size,
                maxSize: this.responseCache.max,
                hitRate: this.calculateHitRate(this.responseCache),
            },
            config: this.config,
        };
    }
    clearAllCaches() {
        this.intentCache.clear();
        this.followUpCache.clear();
        this.entitySearchCache.clear();
        this.responseCache.clear();
        this.logger.log('All caches cleared');
    }
    clearCache(cacheType) {
        switch (cacheType) {
            case 'intent':
                this.intentCache.clear();
                break;
            case 'followUp':
                this.followUpCache.clear();
                break;
            case 'entitySearch':
                this.entitySearchCache.clear();
                break;
            case 'response':
                this.responseCache.clear();
                break;
        }
        this.logger.log(`${cacheType} cache cleared`);
    }
    async warmUpCaches(commonQueries) {
        this.logger.log(`Warming up caches with ${commonQueries.length} common queries`);
        for (const query of commonQueries) {
            try {
                const normalizedQuery = this.normalizeSearchQuery(query);
                const intentKey = this.generateIntentKey(query, 'common');
            }
            catch (error) {
                this.logger.warn(`Failed to warm up cache for query: ${query}`, error);
            }
        }
    }
    hashString(str) {
        let hash = 0;
        if (str.length === 0)
            return hash.toString();
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(36);
    }
    normalizeSearchQuery(query) {
        return query
            .toLowerCase()
            .trim()
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s]/g, '');
    }
    calculateHitRate(cache) {
        return cache.size > 0 ? Math.min(cache.size / cache.max * 100, 100) : 0;
    }
    extractDiscussedTopicsFromContext(context) {
        const topics = new Set();
        const messages = context.messages || [];
        const topicKeywords = {
            'programming': ['code', 'coding', 'programming', 'development'],
            'education': ['education', 'teaching', 'learning', 'student'],
            'business': ['business', 'work', 'company', 'industry'],
            'budget': ['budget', 'cost', 'price', 'expensive', 'cheap'],
            'technical_level': ['beginner', 'intermediate', 'advanced', 'expert'],
        };
        messages.forEach((msg) => {
            const content = msg.content?.toLowerCase() || '';
            Object.entries(topicKeywords).forEach(([topic, keywords]) => {
                if (keywords.some(keyword => content.includes(keyword))) {
                    topics.add(topic);
                }
            });
        });
        return Array.from(topics).sort();
    }
};
exports.ChatCacheService = ChatCacheService;
exports.ChatCacheService = ChatCacheService = ChatCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ChatCacheService);
//# sourceMappingURL=chat-cache.service.js.map