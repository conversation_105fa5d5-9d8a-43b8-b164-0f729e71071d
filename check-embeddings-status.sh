#!/bin/bash

# Check embedding status in the database
# This script helps identify if entities have vector embeddings

BASE_URL="https://ai-nav.onrender.com"
JWT_TOKEN="eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3717khy7yMNKYgKQtkBr7nnSjyiuOJ3xLpWI9p5CaEg"

echo "🔍 EMBEDDING STATUS DIAGNOSTIC"
echo "=============================="
echo ""

# Test specific entities that should exist
echo "📊 Testing specific known entities..."
echo ""

# Test 1: Try vector search with very broad terms
echo "🔍 TEST 1: Broad Vector Search Tests"
echo "-----------------------------------"

test_broad_vector() {
    local query="$1"
    echo "Testing: '$query'"
    
    local payload=$(cat <<EOF
{
  "query": "$query",
  "limit": 10
}
EOF
)
    
    local response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$payload" \
        "$BASE_URL/entities/vector-search")
    
    local count=$(echo "$response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
    echo "  Results: $count entities"
    
    if [ "$count" -gt 0 ]; then
        echo "  ✅ Found entities"
        echo "  📝 Names: $(echo "$response" | grep -o '"name":"[^"]*"' | head -3 | cut -d'"' -f4 | tr '\n' ', ')"
    else
        echo "  ❌ No results"
    fi
    echo ""
}

# Test with very common terms
test_broad_vector "AI"
test_broad_vector "tool"
test_broad_vector "chat"
test_broad_vector "text"
test_broad_vector "image"

# Test 2: Check if any entities have embeddings by testing similarity threshold
echo "🔍 TEST 2: Embedding Threshold Test"
echo "-----------------------------------"
echo "Testing with very low similarity threshold..."

payload=$(cat <<EOF
{
  "query": "artificial intelligence",
  "limit": 20
}
EOF
)

response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $JWT_TOKEN" \
    -d "$payload" \
    "$BASE_URL/entities/vector-search")

count=$(echo "$response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
echo "Results with 'artificial intelligence': $count entities"

if [ "$count" -gt 0 ]; then
    echo "✅ Vector search is working with some entities"
    echo "📝 Sample entities with embeddings:"
    echo "$response" | grep -o '"name":"[^"]*"' | head -5
    echo ""
    echo "📊 Similarity scores:"
    echo "$response" | grep -o '"similarity":[0-9.]*' | head -5
else
    echo "❌ No entities found even with broad search"
    echo "📝 This suggests most entities lack embeddings"
fi
echo ""

# Test 3: Compare with keyword search for same terms
echo "🔍 TEST 3: Keyword vs Vector Comparison"
echo "---------------------------------------"

compare_search() {
    local query="$1"
    echo "Comparing searches for: '$query'"
    
    # Keyword search
    local keyword_response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" \
        "$BASE_URL/entities?searchTerm=$query&limit=5")
    local keyword_count=$(echo "$keyword_response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
    
    # Vector search
    local vector_payload=$(cat <<EOF
{
  "query": "$query",
  "limit": 5
}
EOF
)
    local vector_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $JWT_TOKEN" \
        -d "$vector_payload" \
        "$BASE_URL/entities/vector-search")
    local vector_count=$(echo "$vector_response" | grep -o '"id":"[^"]*"' | wc -l | tr -d ' ')
    
    echo "  Keyword search: $keyword_count results"
    echo "  Vector search:  $vector_count results"
    
    if [ "$keyword_count" -gt 0 ] && [ "$vector_count" -eq 0 ]; then
        echo "  ⚠️  Entities exist but lack embeddings"
    elif [ "$keyword_count" -gt 0 ] && [ "$vector_count" -gt 0 ]; then
        echo "  ✅ Both searches working"
    elif [ "$keyword_count" -eq 0 ] && [ "$vector_count" -eq 0 ]; then
        echo "  ❌ No entities found for this term"
    else
        echo "  🤔 Unusual: Vector found more than keyword"
    fi
    echo ""
}

compare_search "video"
compare_search "generation"
compare_search "OpenAI"
compare_search "GPT"

# Test 4: Check entity details for embedding info
echo "🔍 TEST 4: Entity Details Check"
echo "-------------------------------"
echo "Checking first few entities for embedding-related info..."

response=$(curl -s -H "Authorization: Bearer $JWT_TOKEN" "$BASE_URL/entities?limit=3")
echo "$response" | grep -o '"name":"[^"]*"' | while read -r name_line; do
    entity_name=$(echo "$name_line" | cut -d'"' -f4)
    echo "📝 Entity: $entity_name"
done
echo ""

echo "🎯 EMBEDDING DIAGNOSIS SUMMARY"
echo "=============================="
echo ""
echo "Based on the tests above:"
echo "• If vector search returns 0 results for common terms like 'AI', 'tool', 'chat'"
echo "  → Most entities lack vector embeddings"
echo ""
echo "• If keyword search works but vector search doesn't"
echo "  → Entities exist but embeddings are missing or corrupted"
echo ""
echo "• If vector search works for some entities but not others"
echo "  → Partial embedding coverage (some entities have embeddings, others don't)"
echo ""
echo "💡 NEXT STEPS:"
echo "1. If embeddings are missing: Run embedding generation job"
echo "2. If embeddings exist but search fails: Check vector similarity threshold"
echo "3. If partial coverage: Identify which entities need embedding generation"
